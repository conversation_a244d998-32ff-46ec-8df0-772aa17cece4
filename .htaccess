RewriteEngine On

# Handle API routes
RewriteRule ^api/(.*)$ api/$1 [L]

# Handle file extensions
AddType application/x-httpd-php .php

# Security headers
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"

# Enable CORS for API calls
Header add Access-Control-Allow-Origin "*"
Header add Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header add Access-Control-Allow-Headers "Content-Type, Authorization"

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
</IfModule>