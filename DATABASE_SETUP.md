# BhaviCRM MySQL Database Implementation

## Overview
This document provides comprehensive instructions for setting up and managing the MySQL database for BhaviCRM.

## Prerequisites
- MySQL 5.7+ or MariaDB 10.2+
- PHP 8.0+ with PDO MySQL extension
- Web server (Apache/Nginx) or PHP built-in server

## Quick Setup

### Option 1: Installation Wizard (Recommended)
1. Navigate to `http://localhost:8000/setup-wizard.php`
2. Follow the step-by-step installation process
3. The wizard will:
   - Test database connection
   - Create database if needed
   - Install schema and procedures
   - Set up admin user and company

### Option 2: Manual Setup
1. Create MySQL database:
   ```sql
   CREATE DATABASE business_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. Import schema:
   ```bash
   mysql -u username -p business_management < database/schema.sql
   mysql -u username -p business_management < database/procedures.sql
   ```

3. Configure database connection in `config/database.php`

## Database Structure

### Core Tables
- **companies** - Multi-tenant company information
- **users** - User accounts and authentication
- **customers** - Customer/client information
- **items** - Products and services catalog
- **invoices** - Invoice records
- **invoice_items** - Invoice line items
- **quotations** - Quote/estimate records
- **quotation_items** - Quote line items
- **leads** - Sales leads and prospects
- **contracts** - Contract management

### Additional Tables
- **payment_records** - Payment tracking
- **settings** - Application configuration
- **activity_log** - Audit trail
- **email_templates** - Email templates
- **attachments** - File attachments

## Features

### 1. Multi-Tenant Support
- Each company has isolated data
- Shared user authentication
- Company-specific settings

### 2. Automated Calculations
- Invoice/quotation totals
- Tax calculations
- Payment tracking
- Status updates

### 3. Stored Procedures
- `UpdateInvoiceTotals(invoice_id)` - Recalculate invoice totals
- `UpdateQuotationTotals(quotation_id)` - Recalculate quotation totals
- `UpdateInvoiceStatus(invoice_id)` - Update invoice status based on payments
- `RecordPayment(...)` - Record payment and update invoice
- `GetNextInvoiceNumber(company_id, prefix)` - Generate sequential invoice numbers
- `GetNextQuotationNumber(company_id, prefix)` - Generate sequential quote numbers
- `LogActivity(...)` - Log user activities
- `GetCompanyStats(company_id)` - Get company statistics

### 4. Data Integrity
- Foreign key constraints
- Proper indexing for performance
- UTF8MB4 character set for full Unicode support
- Cascading deletes where appropriate

## Management Tools

### Database Manager Dashboard
Access: `http://localhost:8000/database-manager.html`

Features:
- Real-time database status
- Table information and statistics
- Health checks
- Backup creation
- Schema installation
- Connection testing

### API Endpoints
- `api/database/test-connection.php` - Test database connectivity
- `api/database/create-database.php` - Create database
- `api/database/install-schema.php` - Install/update schema
- `api/database/backup.php` - Create database backup
- `api/database/restore.php` - Restore from backup
- `api/database/status.php` - Get database status and health

## Configuration

### Database Settings
Edit `config/database.php`:
```php
$host = 'localhost';
$dbname = 'business_management';
$username = 'your_username';
$password = 'your_password';
```

### Application Settings
Default settings are stored in the `settings` table:
- Invoice/quotation prefixes
- Default currency and tax rates
- Company timezone
- Email templates
- Backup preferences

## Backup and Restore

### Automated Backups
```php
// Create backup via API
POST api/database/backup.php
```

### Manual Backup
```bash
mysqldump -u username -p business_management > backup.sql
```

### Restore
```php
// Restore via API
POST api/database/restore.php
{
  "backup_file": "backup_2024-01-15_10-30-00.sql"
}
```

## Performance Optimization

### Indexes
All tables include appropriate indexes for:
- Primary keys
- Foreign keys
- Frequently queried columns
- Date ranges
- Status fields

### Query Optimization
- Use prepared statements
- Limit result sets
- Proper JOIN usage
- Index-friendly WHERE clauses

## Security

### Best Practices
1. Use strong database passwords
2. Limit database user privileges
3. Enable SSL connections
4. Regular security updates
5. Monitor access logs

### User Permissions
```sql
-- Create dedicated database user
CREATE USER 'bhavicrm'@'localhost' IDENTIFIED BY 'strong_password';
GRANT SELECT, INSERT, UPDATE, DELETE ON business_management.* TO 'bhavicrm'@'localhost';
FLUSH PRIVILEGES;
```

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check MySQL service is running
   - Verify credentials
   - Check firewall settings
   - Ensure PDO MySQL extension is installed

2. **Schema Installation Failed**
   - Check user permissions
   - Verify MySQL version compatibility
   - Check for existing tables

3. **Performance Issues**
   - Analyze slow queries
   - Check index usage
   - Monitor table sizes
   - Consider partitioning for large datasets

### Error Logs
- PHP errors: Check web server error logs
- MySQL errors: Check MySQL error log
- Application logs: Check `activity_log` table

## Maintenance

### Regular Tasks
1. **Daily**
   - Monitor database size
   - Check error logs
   - Verify backups

2. **Weekly**
   - Analyze performance
   - Clean up old logs
   - Update statistics

3. **Monthly**
   - Full database backup
   - Security audit
   - Performance review

### Health Checks
Use the database manager dashboard to monitor:
- Connection status
- Table integrity
- Orphaned records
- Storage usage
- Recent activity

## Migration and Upgrades

### Version Updates
1. Backup current database
2. Test upgrade on copy
3. Apply schema changes
4. Update stored procedures
5. Verify data integrity

### Data Migration
- Export/import utilities available
- API endpoints for bulk operations
- Validation and error handling

## Support

### Documentation
- Schema documentation in `database/schema.sql`
- Procedure documentation in `database/procedures.sql`
- API documentation in respective PHP files

### Monitoring
- Built-in health checks
- Performance metrics
- Activity logging
- Error tracking

This MySQL implementation provides a robust, scalable foundation for the BhaviCRM application with comprehensive management tools and best practices for security and performance.