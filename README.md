# Business Management Application - PHP/MySQL

A comprehensive business management application built with PHP, MySQL, and React for managing customers, invoices, quotations, leads, and contracts.

## Features

- **Customer Management** - Add, edit, and track customer information
- **Invoice System** - Create and manage invoices with automatic calculations
- **Quotation Management** - Generate professional quotes for clients
- **Lead Tracking** - Manage sales leads and conversion pipeline
- **Contract Management** - Handle contracts and agreements
- **Item Catalog** - Maintain product/service inventory
- **Reporting Dashboard** - Business analytics and insights
- **PDF Export** - Generate PDF documents for invoices and quotes

## Technology Stack

- **Frontend**: React 18, TailwindCSS
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Server**: Apache/Nginx

## Quick Setup

1. **Upload files** to your web server directory
2. **Visit setup.php** in your browser for guided installation
3. **Configure database** credentials through the setup wizard
4. **Access the application** via index.html

## Manual Installation

1. Create MySQL database named `business_management`
2. Update `config/database.php` with your credentials:
```php
$host = 'localhost';
$dbname = 'business_management';
$username = 'your_username';
$password = 'your_password';
```
3. Run `install.php` to create tables and sample data

## File Structure

```
/
├── index.html              # Main application
├── setup.php              # Setup wizard
├── install.php            # Database installer
├── config/
│   └── database.php        # Database config
├── api/                    # PHP API endpoints
│   ├── customers.php
│   ├── invoices.php
│   ├── quotations.php
│   ├── leads.php
│   ├── contracts.php
│   ├── items.php
│   ├── reports.php
│   ├── settings.php
│   └── test.php
├── database/
│   ├── schema.sql         # Database schema
│   └── procedures.sql     # Stored procedures
└── components/            # React components
```

## API Endpoints

All endpoints are in `/api/` directory:
- `customers.php` - Customer CRUD operations
- `invoices.php` - Invoice management
- `quotations.php` - Quote management
- `leads.php` - Lead tracking
- `contracts.php` - Contract management
- `items.php` - Product/service catalog
- `reports.php` - Business reports
- `settings.php` - Company settings

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- mod_rewrite enabled (for .htaccess)

## Support

For issues or questions, please check the setup wizard first or verify your PHP and MySQL configuration.