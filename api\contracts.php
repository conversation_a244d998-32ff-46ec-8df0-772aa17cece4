<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                $stmt = $pdo->prepare("
                    SELECT c.*, cu.name as customer_name 
                    FROM contracts c 
                    LEFT JOIN customers cu ON c.customer_id = cu.id 
                    WHERE c.id = ?
                ");
                $stmt->execute([$_GET['id']]);
                $contract = $stmt->fetch();
                
                if($contract) {
                    echo json_encode($contract);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Contract not found']);
                }
            } else {
                $stmt = $pdo->query("
                    SELECT c.*, cu.name as customer_name 
                    FROM contracts c 
                    LEFT JOIN customers cu ON c.customer_id = cu.id 
                    ORDER BY c.created_at DESC
                ");
                $contracts = $stmt->fetchAll();
                echo json_encode($contracts);
            }
            break;
            
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("
                INSERT INTO contracts (company_id, contract_number, customer_id, title, start_date, end_date, value, terms, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                1,
                $data['contract_number'],
                $data['customer_id'],
                $data['title'],
                $data['start_date'],
                $data['end_date'],
                $data['value'] ?? 0,
                $data['terms'] ?? '',
                1
            ]);
            
            $id = $pdo->lastInsertId();
            $stmt = $pdo->prepare("SELECT * FROM contracts WHERE id = ?");
            $stmt->execute([$id]);
            $contract = $stmt->fetch();
            
            echo json_encode($contract);
            break;
            
        case 'PUT':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("
                UPDATE contracts 
                SET title = ?, start_date = ?, end_date = ?, value = ?, status = ?, terms = ?
                WHERE id = ?
            ");
            $stmt->execute([
                $data['title'],
                $data['start_date'],
                $data['end_date'],
                $data['value'],
                $data['status'],
                $data['terms'],
                $data['id']
            ]);
            
            echo json_encode(['success' => true]);
            break;
            
        case 'DELETE':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("DELETE FROM contracts WHERE id = ?");
            $stmt->execute([$data['id']]);
            
            echo json_encode(['success' => true]);
            break;
    }
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>