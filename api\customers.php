<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
                $stmt->execute([$_GET['id']]);
                $customer = $stmt->fetch();
                
                if($customer) {
                    echo json_encode($customer);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Customer not found']);
                }
            } else {
                $stmt = $pdo->query("SELECT * FROM customers ORDER BY created_at DESC");
                $customers = $stmt->fetchAll();
                echo json_encode($customers);
            }
            break;
            
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON']);
                break;
            }

            $required_fields = ['name', 'email', 'phone', 'address', 'company'];
            foreach ($required_fields as $field) {
                if (empty($data[$field])) {
                    http_response_code(400);
                    echo json_encode(['error' => "Missing required field: $field"]);
                    break 2;
                }
            }
            
            $stmt = $pdo->prepare("INSERT INTO customers (company_id, name, email, phone, address, company, notes) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([
                1, // Default company_id
                $data['name'],
                $data['email'],
                $data['phone'],
                $data['address'],
                $data['company'],
                $data['notes'] ?? ''
            ]);
            
            $id = $pdo->lastInsertId();
            $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
            $stmt->execute([$id]);
            $customer = $stmt->fetch();
            
            echo json_encode($customer);
            break;
            
        case 'PUT':
            $data = json_decode(file_get_contents('php://input'), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON']);
                break;
            }

            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Missing required field: id']);
                break;
            }
            
            $stmt = $pdo->prepare("UPDATE customers SET name = ?, email = ?, phone = ?, address = ?, company = ?, notes = ? WHERE id = ?");
            $stmt->execute([
                $data['name'],
                $data['email'],
                $data['phone'],
                $data['address'],
                $data['company'],
                $data['notes'] ?? '',
                $data['id']
            ]);
            
            $stmt = $pdo->prepare("SELECT * FROM customers WHERE id = ?");
            $stmt->execute([$data['id']]);
            $customer = $stmt->fetch();
            
            echo json_encode($customer);
            break;
            
        case 'DELETE':
            $data = json_decode(file_get_contents('php://input'), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                http_response_code(400);
                echo json_encode(['error' => 'Invalid JSON']);
                break;
            }

            if (empty($data['id'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Missing required field: id']);
                break;
            }
            
            $stmt = $pdo->prepare("DELETE FROM customers WHERE id = ?");
            $stmt->execute([$data['id']]);
            
            echo json_encode(['success' => true]);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>