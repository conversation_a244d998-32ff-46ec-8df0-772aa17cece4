<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    $backupDir = '../../backups';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d_H-i-s');
    $backupFile = $backupDir . "/backup_$timestamp.sql";
    
    // Get database name from PDO
    $stmt = $pdo->query("SELECT DATABASE() as db_name");
    $result = $stmt->fetch();
    $dbname = $result['db_name'];
    
    // Get all tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $backup = "-- BhaviCRM Database Backup\n";
    $backup .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
    $backup .= "-- Database: $dbname\n\n";
    $backup .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    foreach ($tables as $table) {
        // Get table structure
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch();
        $backup .= "-- Table structure for `$table`\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        $backup .= $row['Create Table'] . ";\n\n";
        
        // Get table data
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $backup .= "-- Data for table `$table`\n";
            
            foreach ($rows as $row) {
                $values = array_map(function($value) use ($pdo) {
                    return $value === null ? 'NULL' : $pdo->quote($value);
                }, array_values($row));
                
                $backup .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
            }
            $backup .= "\n";
        }
    }
    
    $backup .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    // Write backup file
    if (file_put_contents($backupFile, $backup)) {
        $fileSize = filesize($backupFile);
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Database backup created successfully',
            'filename' => basename($backupFile),
            'file_size' => $fileSize,
            'tables_backed_up' => count($tables),
            'backup_path' => $backupFile
        ]);
    } else {
        throw new Exception('Failed to write backup file');
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Backup failed: ' . $e->getMessage()
    ]);
}
?>