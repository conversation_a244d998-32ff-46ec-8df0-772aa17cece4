<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $host = $input['host'] ?? 'localhost';
    $username = $input['username'] ?? 'root';
    $password = $input['password'] ?? 'root';
    $dbname = $input['dbname'] ?? 'business_management';

    $schemaPath = realpath(__DIR__ . '/../../database/schema.sql');
    if (!$schemaPath || !file_exists($schemaPath)) {
        throw new Exception('Schema file not found.');
    }

    $proceduresPath = realpath(__DIR__ . '/../../database/procedures.sql');
    if (!$proceduresPath || !file_exists($proceduresPath)) {
        throw new Exception('Procedures file not found.');
    }

    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    function execute_sql_script($pdo, $sql_file_path) {
        $sql = file_get_contents($sql_file_path);
        $queries = preg_split('/DELIMITER\s+\S+/', $sql);
        $delimiter = ';';

        foreach ($queries as $i => $query) {
            if (preg_match('/DELIMITER\s+(\S+)/', $sql, $matches, PREG_OFFSET_CAPTURE, ($i > 0 ? strlen($queries[$i-1]) : 0) ) ) {
                 $delimiter = $matches[1][0];
            }
            
            $statements = explode($delimiter, $query);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if ($statement) {
                    try {
                        $pdo->exec($statement);
                    } catch (PDOException $e) {
                        // Ignore errors for dropping non-existent tables
                        if ($e->getCode() !== '42S02') {
                            throw $e;
                        }
                    }
                }
            }
        }
    }

    // Execute schema.sql
    execute_sql_script($pdo, $schemaPath);

    // Execute procedures.sql
    execute_sql_script($pdo, $proceduresPath);

    // Verify installation by checking tables
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Check for required tables
    $requiredTables = ['companies', 'users', 'customers', 'items', 'invoices', 'quotations'];
    $missingTables = array_diff($requiredTables, $tables);
    
    if (empty($missingTables)) {
        echo json_encode([
            'status' => 'success',
            'message' => 'Database schema installed successfully using mysql client.',
            'tables_created' => count($tables),
            'tables' => $tables
        ]);
    } else {
        throw new Exception('Some required tables were not created: ' . implode(', ', $missingTables));
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Schema installation failed: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>