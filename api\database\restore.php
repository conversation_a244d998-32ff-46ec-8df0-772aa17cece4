<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../../config/database.php';

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $backupFile = $input['backup_file'] ?? '';
    
    if (empty($backupFile)) {
        throw new Exception('Backup file not specified');
    }
    
    $backupPath = '../../backups/' . basename($backupFile);
    
    if (!file_exists($backupPath)) {
        throw new Exception('Backup file not found: ' . $backupFile);
    }
    
    // Read backup file
    $sql = file_get_contents($backupPath);
    if (!$sql) {
        throw new Exception('Failed to read backup file');
    }
    
    // Execute SQL statements
    $statements = explode(';', $sql);
    $executedStatements = 0;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                $executedStatements++;
            } catch (PDOException $e) {
                // Log error but continue with other statements
                error_log("SQL Error in restore: " . $e->getMessage() . " - Statement: " . substr($statement, 0, 100));
            }
        }
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Database restored successfully',
        'backup_file' => $backupFile,
        'statements_executed' => $executedStatements
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Restore failed: ' . $e->getMessage()
    ]);
}
?>