<?php
require_once __DIR__ . '/../../config/database.php';

try {
    // First get schema to drop and recreate tables
    $schema = file_get_contents(__DIR__ . '/../../database/schema.sql');
    $pdo->exec($schema);
    
    // Then seed the data
    $sql = file_get_contents(__DIR__ . '/../../database/seed.sql');
    $pdo->exec($sql);
    echo "Database seeded successfully.";
} catch (PDOException $e) {
    die("Database seeding failed: " . $e->getMessage());
}
?>
