<?php
header('Content-Type: application/json');
require_once '../../config/database.php';

try {
    // Get database information
    $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as mysql_version");
    $dbInfo = $stmt->fetch();
    
    // Get table information
    $stmt = $pdo->query("
        SELECT 
            TABLE_NAME as table_name,
            TABLE_ROWS as row_count,
            ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as size_mb
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = DATABASE()
        ORDER BY TABLE_NAME
    ");
    $tables = $stmt->fetchAll();
    
    // Get total database size
    $stmt = $pdo->query("
        SELECT 
            ROUND(SUM(DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as total_size_mb
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = DATABASE()
    ");
    $sizeInfo = $stmt->fetch();
    
    // Check for required tables
    $requiredTables = [
        'companies', 'users', 'customers', 'items', 'invoices', 'invoice_items',
        'quotations', 'quotation_items', 'leads', 'contracts', 'payment_records',
        'settings', 'activity_log', 'email_templates', 'attachments'
    ];
    
    $existingTables = array_column($tables, 'table_name');
    $missingTables = array_diff($requiredTables, $existingTables);
    
    // Get recent activity
    $stmt = $pdo->query("
        SELECT COUNT(*) as activity_count 
        FROM activity_log 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $activityInfo = $stmt->fetch();
    
    // Get statistics
    $stats = [];
    foreach (['customers', 'invoices', 'quotations', 'leads'] as $table) {
        if (in_array($table, $existingTables)) {
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $result = $stmt->fetch();
            $stats[$table] = $result['count'];
        }
    }
    
    // Check database health
    $health = [
        'status' => 'healthy',
        'issues' => []
    ];
    
    if (!empty($missingTables)) {
        $health['status'] = 'warning';
        $health['issues'][] = 'Missing tables: ' . implode(', ', $missingTables);
    }
    
    if ($sizeInfo['total_size_mb'] > 1000) {
        $health['issues'][] = 'Database size is large (' . $sizeInfo['total_size_mb'] . ' MB)';
    }
    
    // Check for orphaned records
    $orphanChecks = [
        'invoice_items without invoices' => "SELECT COUNT(*) as count FROM invoice_items i LEFT JOIN invoices inv ON i.invoice_id = inv.id WHERE inv.id IS NULL",
        'quotation_items without quotations' => "SELECT COUNT(*) as count FROM quotation_items q LEFT JOIN quotations quo ON q.quotation_id = quo.id WHERE quo.id IS NULL"
    ];
    
    foreach ($orphanChecks as $check => $query) {
        try {
            $stmt = $pdo->query($query);
            $result = $stmt->fetch();
            if ($result['count'] > 0) {
                $health['status'] = 'warning';
                $health['issues'][] = "Found {$result['count']} $check";
            }
        } catch (Exception $e) {
            // Skip if table doesn't exist
        }
    }
    
    echo json_encode([
        'status' => 'success',
        'database' => [
            'name' => $dbInfo['db_name'],
            'mysql_version' => $dbInfo['mysql_version'],
            'total_size_mb' => $sizeInfo['total_size_mb'],
            'table_count' => count($tables)
        ],
        'tables' => $tables,
        'statistics' => $stats,
        'health' => $health,
        'recent_activity' => $activityInfo['activity_count'],
        'missing_tables' => $missingTables,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database status check failed: ' . $e->getMessage()
    ]);
}
?>