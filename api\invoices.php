<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                $stmt = $pdo->prepare("
                    SELECT i.*, c.name as customer_name 
                    FROM invoices i 
                    LEFT JOIN customers c ON i.customer_id = c.id 
                    WHERE i.id = ?
                ");
                $stmt->execute([$_GET['id']]);
                $invoice = $stmt->fetch();
                
                if($invoice) {
                    // Get invoice items
                    $stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = ? ORDER BY sort_order");
                    $stmt->execute([$_GET['id']]);
                    $invoice['items'] = $stmt->fetchAll();
                    
                    echo json_encode($invoice);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Invoice not found']);
                }
            } else {
                $stmt = $pdo->query("
                    SELECT i.*, c.name as customer_name 
                    FROM invoices i 
                    LEFT JOIN customers c ON i.customer_id = c.id 
                    ORDER BY i.created_at DESC
                ");
                $invoices = $stmt->fetchAll();
                echo json_encode($invoices);
            }
            break;
            
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $pdo->beginTransaction();
            
            // Insert invoice
            $stmt = $pdo->prepare("
                INSERT INTO invoices (company_id, invoice_number, customer_id, issue_date, due_date, tax_rate, notes, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                1, // Default company_id
                $data['invoice_number'],
                $data['customer_id'],
                $data['issue_date'],
                $data['due_date'],
                $data['tax_rate'] ?? 0,
                $data['notes'] ?? '',
                1 // Default user_id
            ]);
            
            $invoiceId = $pdo->lastInsertId();
            
            // Insert invoice items
            if(isset($data['items'])) {
                $stmt = $pdo->prepare("
                    INSERT INTO invoice_items (invoice_id, description, quantity, price, total, sort_order) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                foreach($data['items'] as $index => $item) {
                    $stmt->execute([
                        $invoiceId,
                        $item['description'],
                        $item['quantity'],
                        $item['price'],
                        $item['total'],
                        $index
                    ]);
                }
            }
            
            // Update totals
            $pdo->exec("CALL UpdateInvoiceTotals($invoiceId)");
            
            $pdo->commit();
            
            // Return created invoice
            $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ?");
            $stmt->execute([$invoiceId]);
            $invoice = $stmt->fetch();
            
            echo json_encode($invoice);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch(Exception $e) {
    if($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>