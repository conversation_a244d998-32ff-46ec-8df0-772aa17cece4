<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch($method) {
        case 'GET':
            $stmt = $pdo->query("
                SELECT * FROM leads 
                ORDER BY created_at DESC
            ");
            $leads = $stmt->fetchAll();
            echo json_encode($leads);
            break;
            
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("
                INSERT INTO leads (company_id, name, email, phone, company, source, status, notes) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                1,
                $data['name'],
                $data['email'],
                $data['phone'] ?? '',
                $data['company'] ?? '',
                $data['source'] ?? 'website',
                $data['status'] ?? 'new',
                $data['notes'] ?? ''
            ]);
            
            $id = $pdo->lastInsertId();
            $stmt = $pdo->prepare("SELECT * FROM leads WHERE id = ?");
            $stmt->execute([$id]);
            $lead = $stmt->fetch();
            
            echo json_encode($lead);
            break;
            
        case 'PUT':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("
                UPDATE leads 
                SET name = ?, email = ?, phone = ?, company = ?, source = ?, status = ?, notes = ? 
                WHERE id = ?
            ");
            $stmt->execute([
                $data['name'],
                $data['email'],
                $data['phone'],
                $data['company'],
                $data['source'],
                $data['status'],
                $data['notes'],
                $data['id']
            ]);
            
            echo json_encode(['success' => true]);
            break;
            
        case 'DELETE':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $stmt = $pdo->prepare("DELETE FROM leads WHERE id = ?");
            $stmt->execute([$data['id']]);
            
            echo json_encode(['success' => true]);
            break;
    }
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>