<?php
function authenticate() {
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';

    if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
        $token = $matches[1];
        
        try {
            $secret = 'your-secret-key';
            list($header, $payload, $signature) = explode('.', $token);
            
            $validSignature = hash_hmac('sha256', $header . "." . $payload, $secret, true);
            $validSignature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($validSignature));
            
            if ($signature !== $validSignature) {
                throw new Exception('Invalid token signature');
            }
            
            $decoded = json_decode(base64_decode($payload), true);
            
            // Verify token hasn't expired
            if (isset($decoded['exp']) && $decoded['exp'] < time()) {
                throw new Exception('Token has expired');
            }
            
            return $decoded;
        } catch (Exception $e) {
            http_response_code(401);
            echo json_encode(['error' => 'Unauthorized: ' . $e->getMessage()]);
            exit;
        }
    }

    http_response_code(401);
    echo json_encode(['error' => 'Authorization header missing']);
    exit;
}

function authorize($roles = []) {
    $user = authenticate();
    
    if (!empty($roles) && !in_array($user['role'], $roles)) {
        http_response_code(403);
        echo json_encode(['error' => 'Forbidden']);
        exit;
    }
    
    return $user;
}
