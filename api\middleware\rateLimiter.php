<?php
function rateLimit($maxRequests = 100, $timeWindow = 60) {
    // Use Redis if available, otherwise fall back to file-based storage
    $storage = new RateLimitStorage();
    
    $ip = $_SERVER['REMOTE_ADDR'];
    $key = "rate_limit_$ip";
    
    $current = $storage->get($key);
    
    if ($current === false) {
        $storage->set($key, 1, $timeWindow);
        return true;
    }
    
    if ($current >= $maxRequests) {
        http_response_code(429);
        echo json_encode(['error' => 'Too many requests']);
        exit;
    }
    
    $storage->increment($key);
    return true;
}

class RateLimitStorage {
    private $redis;
    
    public function __construct() {
        if (class_exists('Redis') && extension_loaded('redis')) {
            $this->redis = new Redis();
            try {
                $this->redis->connect('127.0.0.1', 6379);
            } catch (Exception $e) {
                $this->redis = null;
            }
        }
    }
    
    public function get($key) {
        if ($this->redis) {
            return $this->redis->get($key);
        }
        
        // Fallback to file storage
        $file = sys_get_temp_dir() . '/ratelimit_' . md5($key) . '.tmp';
        if (!file_exists($file)) return false;
        
        $data = json_decode(file_get_contents($file), true);
        if ($data['expire'] < time()) {
            unlink($file);
            return false;
        }
        
        return $data['count'];
    }
    
    public function set($key, $value, $ttl) {
        if ($this->redis) {
            return $this->redis->setex($key, $ttl, $value);
        }
        
        // Fallback to file storage
        $file = sys_get_temp_dir() . '/ratelimit_' . md5($key) . '.tmp';
        file_put_contents($file, json_encode([
            'count' => $value,
            'expire' => time() + $ttl
        ]));
    }
    
    public function increment($key) {
        if ($this->redis) {
            return $this->redis->incr($key);
        }
        
        // Fallback to file storage
        $file = sys_get_temp_dir() . '/ratelimit_' . md5($key) . '.tmp';
        $data = json_decode(file_get_contents($file), true);
        $data['count']++;
        file_put_contents($file, json_encode($data));
    }
}
