<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch($method) {
        case 'GET':
            if(isset($_GET['id'])) {
                $stmt = $pdo->prepare("
                    SELECT q.*, c.name as customer_name 
                    FROM quotations q 
                    LEFT JOIN customers c ON q.customer_id = c.id 
                    WHERE q.id = ?
                ");
                $stmt->execute([$_GET['id']]);
                $quotation = $stmt->fetch();
                
                if($quotation) {
                    // Get quotation items
                    $stmt = $pdo->prepare("SELECT * FROM quotation_items WHERE quotation_id = ? ORDER BY sort_order");
                    $stmt->execute([$_GET['id']]);
                    $quotation['items'] = $stmt->fetchAll();
                    
                    echo json_encode($quotation);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'Quotation not found']);
                }
            } else {
                $stmt = $pdo->query("
                    SELECT q.*, c.name as customer_name 
                    FROM quotations q 
                    LEFT JOIN customers c ON q.customer_id = c.id 
                    ORDER BY q.created_at DESC
                ");
                $quotations = $stmt->fetchAll();
                echo json_encode($quotations);
            }
            break;
            
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            
            $pdo->beginTransaction();
            
            // Insert quotation
            $stmt = $pdo->prepare("
                INSERT INTO quotations (company_id, quote_number, customer_id, issue_date, expiry_date, tax_rate, notes, created_by) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                1, // Default company_id
                $data['quote_number'],
                $data['customer_id'],
                $data['issue_date'],
                $data['expiry_date'],
                $data['tax_rate'] ?? 0,
                $data['notes'] ?? '',
                1 // Default user_id
            ]);
            
            $quotationId = $pdo->lastInsertId();
            
            // Insert quotation items
            if(isset($data['items'])) {
                $stmt = $pdo->prepare("
                    INSERT INTO quotation_items (quotation_id, description, quantity, price, total, sort_order) 
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                foreach($data['items'] as $index => $item) {
                    $stmt->execute([
                        $quotationId,
                        $item['description'],
                        $item['quantity'],
                        $item['price'],
                        $item['total'],
                        $index
                    ]);
                }
            }
            
            // Update totals
            $pdo->exec("CALL UpdateQuotationTotals($quotationId)");
            
            $pdo->commit();
            
            // Return created quotation
            $stmt = $pdo->prepare("SELECT * FROM quotations WHERE id = ?");
            $stmt->execute([$quotationId]);
            $quotation = $stmt->fetch();
            
            echo json_encode($quotation);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
} catch(Exception $e) {
    if($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>