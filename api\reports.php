<?php
header('Content-Type: application/json');
require_once '../config/database.php';

$startDate = isset($_GET['startDate']) ? $_GET['startDate'] : null;
$endDate = isset($_GET['endDate']) ? $_GET['endDate'] : null;

$whereClause = "WHERE company_id = 1";
$params = [];

if ($startDate && $endDate) {
    $whereClause .= " AND issue_date BETWEEN ? AND ?";
    $params[] = $startDate;
    $params[] = $endDate;
} elseif ($startDate) {
    $whereClause .= " AND issue_date >= ?";
    $params[] = $startDate;
} elseif ($endDate) {
    $whereClause .= " AND issue_date <= ?";
    $params[] = $endDate;
}

try {
    // Sales summary
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_invoices,
            SUM(total) as total_revenue,
            SUM(CASE WHEN status = 'paid' THEN total ELSE 0 END) as paid_revenue,
            SUM(CASE WHEN status = 'overdue' THEN total ELSE 0 END) as overdue_amount
        FROM invoices
        $whereClause
    ");
    $stmt->execute($params);
    $salesSummary = $stmt->fetch();
    
    // Monthly revenue
    $monthlyRevenueStmt = $pdo->prepare("
        SELECT 
            DATE_FORMAT(issue_date, '%Y-%m') as month,
            SUM(total) as revenue
        FROM invoices 
        $whereClause
        GROUP BY DATE_FORMAT(issue_date, '%Y-%m')
        ORDER BY month
    ");
    $monthlyRevenueStmt->execute($params);
    $monthlyRevenue = $monthlyRevenueStmt->fetchAll();
    
    // Total customers
    $totalCustomersStmt = $pdo->query("SELECT COUNT(*) FROM customers WHERE company_id = 1");
    $total_customers = $totalCustomersStmt->fetchColumn();

    // Total leads
    $totalLeadsStmt = $pdo->query("SELECT COUNT(*) FROM leads WHERE company_id = 1");
    $total_leads = $totalLeadsStmt->fetchColumn();

    $salesSummary['total_customers'] = $total_customers;
    $salesSummary['total_leads'] = $total_leads;

    // Top customers
    $topCustomersWhereClause = str_replace(['issue_date', 'company_id'], ['i.issue_date', 'c.company_id'], $whereClause);
    $topCustomersStmt = $pdo->prepare("
        SELECT 
            c.name,
            SUM(i.total) as total_spent,
            COUNT(i.id) as invoice_count
        FROM customers c
        JOIN invoices i ON c.id = i.customer_id
        $topCustomersWhereClause
        GROUP BY c.id
        ORDER BY total_spent DESC
        LIMIT 10
    ");
    $topCustomersStmt->execute($params);
    $topCustomers = $topCustomersStmt->fetchAll();
    
    echo json_encode([
        'salesSummary' => $salesSummary,
        'monthlyRevenue' => $monthlyRevenue,
        'topCustomers' => $topCustomers
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>