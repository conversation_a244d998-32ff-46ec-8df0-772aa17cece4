<?php
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json');
require_once __DIR__ . '/middleware/authMiddleware.php';
require_once __DIR__ . '/middleware/rateLimiter.php';

try {
    require_once __DIR__ . '/../config/database.php';
    $user = authorize(['admin', 'manager']); // Only admin/manager can access settings
    rateLimit(60, 300); // 60 requests per 5 minutes
    
    $method = $_SERVER['REQUEST_METHOD'];
    $company_id = $user['company_id'];

    switch ($method) {
        case 'GET':
            getSettings($pdo, $company_id);
            break;
        case 'POST':
            createSettings($pdo, $company_id);
            break;
        case 'PUT':
            updateSettings($pdo, $company_id);
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method Not Allowed']);
    }
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to process request: ' . $e->getMessage()]);
    exit;
}

function getSettings($pdo, $company_id) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM settings WHERE company_id = ? ORDER BY id DESC LIMIT 1");
        $stmt->execute([$company_id]);
        $settings = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($settings) {
            $settings['notifications'] = json_decode($settings['notifications'], true);
            $settings['templates'] = json_decode($settings['templates'], true);
            $settings['paymentMethods'] = json_decode($settings['paymentMethods'], true);
            $settings['gateways'] = json_decode($settings['gateways'], true);
        }
        echo json_encode($settings ? [$settings] : []);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function createSettings($pdo, $company_id) {
    $data = json_decode(file_get_contents('php://input'), true);
    if ($data === null) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON']);
        return;
    }

    $data['company_id'] = $company_id;
    // Encode JSON fields
    if (isset($data['notifications'])) $data['notifications'] = json_encode($data['notifications']);
    if (isset($data['templates'])) $data['templates'] = json_encode($data['templates']);
    if (isset($data['paymentMethods'])) $data['paymentMethods'] = json_encode($data['paymentMethods']);
    if (isset($data['gateways'])) $data['gateways'] = json_encode($data['gateways']);

    try {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        $stmt = $pdo->prepare("INSERT INTO settings ($columns) VALUES ($placeholders)");
        $stmt->execute($data);
        $data['id'] = $pdo->lastInsertId();
        http_response_code(201);
        echo json_encode($data);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}

function updateSettings($pdo, $company_id) {
    $data = json_decode(file_get_contents('php://input'), true);
    if ($data === null || !isset($data['id'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid JSON or missing ID']);
        return;
    }

    $id = $data['id'];
    unset($data['id']);
    $data['updatedAt'] = date('Y-m-d H:i:s');

    // Encode JSON fields
    if (isset($data['notifications'])) $data['notifications'] = json_encode($data['notifications']);
    if (isset($data['templates'])) $data['templates'] = json_encode($data['templates']);
    if (isset($data['paymentMethods'])) $data['paymentMethods'] = json_encode($data['paymentMethods']);
    if (isset($data['gateways'])) $data['gateways'] = json_encode($data['gateways']);

    try {
        $set_clause = '';
        foreach ($data as $key => $value) {
            $set_clause .= "$key = :$key, ";
        }
        $set_clause = rtrim($set_clause, ', ');

        $data['id'] = $id;
        $data['company_id'] = $company_id;

        $stmt = $pdo->prepare("UPDATE settings SET $set_clause WHERE id = :id AND company_id = :company_id");
        $stmt->execute($data);

        echo json_encode(['success' => true, 'data' => $data]);
    } catch (PDOException $e) {
        http_response_code(500);
        echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
    }
}
?>
