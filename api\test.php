<?php
header('Content-Type: application/json');
require_once '../config/database.php';

try {
    // Test database connection
    $stmt = $pdo->query("SELECT 1");
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Database connection successful',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
}
?>