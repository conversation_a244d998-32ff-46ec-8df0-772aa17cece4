import { useState, useEffect, useCallback } from 'react';
import { createRoot } from 'react-dom/client';
import { AuthProvider } from './components/auth/AuthContext';
import LoginForm from './components/auth/LoginForm';
import MainLayout from './components/layout/MainLayout';
import Notification from './components/common/Notification';
import Dashboard from './pages/Dashboard';
import Customers from './pages/Customers';
import Leads from './pages/Leads';
import Invoices from './pages/Invoices';
import Quotations from './pages/Quotations';
import Contracts from './pages/Contracts';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import './styles/main.css';

function App() {
    const [currentPage, setCurrentPage] = useState('dashboard');
    const [isConnected, setIsConnected] = useState(false);
    const [notification, setNotification] = useState(null);

    const showNotification = useCallback((message, type = 'success') => {
        setNotification({ message, type });
        setTimeout(() => setNotification(null), 5000);
    }, []);

    useEffect(() => {
        const initDB = async () => {
            try {
                const connected = window.databaseConnection 
                    ? await window.databaseConnection.initialize()
                    : true;
                setIsConnected(connected);
            } catch (error) {
                console.error('DB init failed:', error);
                setIsConnected(true);
            }
        };
        initDB();
    }, []);

    useEffect(() => {
        const handleRouteChange = () => {
            const page = window.location.pathname.substring(1) || 'dashboard';
            setCurrentPage(page);
        };

        window.addEventListener('popstate', handleRouteChange);
        handleRouteChange();
        
        return () => window.removeEventListener('popstate', handleRouteChange);
    }, []);

    if (!isConnected) {
        return (
            <div className="flex justify-center items-center h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p>Connecting to database...</p>
                </div>
            </div>
        );
    }

    return (
        <AuthProvider>
            <MainLayout>
                {currentPage === 'dashboard' && <Dashboard />}
                {currentPage === 'customers' && <Customers />}
                {currentPage === 'leads' && <Leads />}
                {currentPage === 'invoices' && <Invoices />}
                {currentPage === 'quotations' && <Quotations />}
                {currentPage === 'contracts' && <Contracts />}
                {currentPage === 'reports' && <Reports />}
                {currentPage === 'settings' && <Settings />}
                
                {notification && (
                    <Notification 
                        message={notification.message} 
                        type={notification.type} 
                        onClose={() => setNotification(null)}
                    />
                )}
            </MainLayout>
        </AuthProvider>
    );
}
