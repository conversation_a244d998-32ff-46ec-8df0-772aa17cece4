import React from 'react';

const AuthContext = React.createContext({
    token: null,
    isAuthenticated: false,
    login: () => {},
    logout: () => {},
    loading: true
});

export const AuthProvider = ({ children }) => {
    const [token, setToken] = React.useState(null);
    const [loading, setLoading] = React.useState(true);

    // Check localStorage for existing token on initial load
    React.useEffect(() => {
        const storedToken = localStorage.getItem('authToken');
        if (storedToken) {
            setToken(storedToken);
        }
        setLoading(false);
    }, []);

    const login = async (email, password) => {
        try {
            const response = await fetch('/api/auth.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email, password })
            });

            if (!response.ok) {
                throw new Error('Login failed');
            }

            const { token } = await response.json();
            localStorage.setItem('authToken', token);
            setToken(token);
            
            return { success: true };
        } catch (err) {
            return { success: false, error: err.message };
        }
    };

    const logout = () => {
        localStorage.removeItem('authToken');
        setToken(null);
        window.location.reload();
    };

    return (
        <AuthContext.Provider value={{
            token,
            isAuthenticated: !!token,
            login,
            logout, 
            loading
        }}>
            {children}
        </AuthContext.Provider>
    );
};

export const useAuth = () => React.useContext(AuthContext);
