function Table({
    columns,
    data,
    onRowClick,
    loading = false,
    emptyMessage = 'No data available',
    sortable = true,
    selectable = false,
    selectedRows = [],
    onRowSelect,
    pagination = false,
    itemsPerPage = 10,
    currentPage = 1,
    onPageChange,
    className = ''
}) {
    try {
        const [sortColumn, setSortColumn] = React.useState(null);
        const [sortDirection, setSortDirection] = React.useState('asc');
        
        const handleSort = (column) => {
            if (!sortable) return;
            
            if (sortColumn === column.key) {
                setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
            } else {
                setSortColumn(column.key);
                setSortDirection('asc');
            }
        };

        const handleRowClick = React.useCallback((item, index) => {
            if (selectable) {
                const isSelected = selectedRows.includes(item.objectId);
                const newSelectedRows = isSelected
                    ? selectedRows.filter(id => id !== item.objectId)
                    : [...selectedRows, item.objectId];
                
                if (onRowSelect) {
                    onRowSelect(newSelectedRows);
                }
            } else if (onRowClick) {
                onRowClick(item, index);
            }
        }, [selectable, selectedRows, onRowSelect, onRowClick]);

        const handleSelectAll = React.useCallback((e) => {
            if (e.target.checked) {
                const allIds = data.map(item => item.objectId);
                if (onRowSelect) {
                    onRowSelect(allIds);
                }
            } else {
                if (onRowSelect) {
                    onRowSelect([]);
                }
            }
        }, [data, onRowSelect]);

        const isRowSelected = React.useCallback((itemId) => {
            return selectedRows.includes(itemId);
        }, [selectedRows]);

        const sortedData = React.useMemo(() => {
            if (!sortColumn || !data) return data;
            
            return [...data].sort((a, b) => {
                const aValue = a.objectData?.[sortColumn] || '';
                const bValue = b.objectData?.[sortColumn] || '';
                
                if (sortDirection === 'asc') {
                    return aValue > bValue ? 1 : -1;
                } else {
                    return aValue < bValue ? 1 : -1;
                }
            });
        }, [data, sortColumn, sortDirection]);

        const paginatedData = React.useMemo(() => {
            if (!pagination) return sortedData;
            
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            return sortedData.slice(startIndex, endIndex);
        }, [sortedData, pagination, currentPage, itemsPerPage]);

        if (loading) {
            return (
                <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            );
        }

        if (!data || data.length === 0) {
            return (
                <div className="text-center py-8 text-gray-500">
                    {emptyMessage}
                </div>
            );
        }

        return (
            <div className={`overflow-x-auto ${className}`}>
                <table className="min-w-full bg-white border border-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            {selectable && (
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <input
                                        type="checkbox"
                                        checked={selectedRows.length === data.length && data.length > 0}
                                        onChange={handleSelectAll}
                                        className="rounded border-gray-300"
                                    />
                                </th>
                            )}
                            {columns.map((column) => (
                                <th
                                    key={column.key}
                                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                                        sortable && column.sortable !== false ? 'cursor-pointer hover:bg-gray-100' : ''
                                    }`}
                                    onClick={() => sortable && column.sortable !== false && handleSort(column)}
                                >
                                    <div className="flex items-center space-x-1">
                                        <span>{column.title}</span>
                                        {sortable && column.sortable !== false && sortColumn === column.key && (
                                            <span className="text-blue-600">
                                                {sortDirection === 'asc' ? '↑' : '↓'}
                                            </span>
                                        )}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {paginatedData.map((item, index) => (
                            <tr
                                key={item.objectId || index}
                                className={`hover:bg-gray-50 cursor-pointer ${
                                    isRowSelected(item.objectId) ? 'bg-blue-50' : ''
                                }`}
                                onClick={() => handleRowClick(item, index)}
                            >
                                {selectable && (
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <input
                                            type="checkbox"
                                            checked={isRowSelected(item.objectId)}
                                            onChange={() => {}}
                                            className="rounded border-gray-300"
                                        />
                                    </td>
                                )}
                                {columns.map((column) => (
                                    <td key={column.key} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {column.render ? column.render(item) : item.objectData?.[column.key] || ''}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        );
    } catch (error) {
        console.error('Table component error:', error);
        return <div className="text-red-500">Error loading table</div>;
    }
}