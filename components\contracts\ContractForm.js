function ContractForm({ contract, onSubmit, onCancel }) {
    try {
        // Define the function before using it in useState
        const generateContractNumber = () => {
            const prefix = 'CT';
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `${prefix}-${timestamp}-${random}`;
        };

        const [formData, setFormData] = React.useState(() => {
            if (contract?.objectData) {
                return {
                    customer: contract.objectData.customer || '',
                    title: contract.objectData.title || '',
                    description: contract.objectData.description || '',
                    startDate: contract.objectData.startDate || '',
                    endDate: contract.objectData.endDate || '',
                    terms: contract.objectData.terms || '',
                    value: contract.objectData.value || 0,
                    status: contract.objectData.status || 'draft',
                    type: contract.objectData.type || 'service',
                    scope: contract.objectData.scope || '',
                    paymentTerms: contract.objectData.paymentTerms || '',
                    renewalTerms: contract.objectData.renewalTerms || '',
                    contractNumber: contract.objectData.contractNumber || generateContractNumber(),
                    customerSignature: contract.objectData.customerSignature || null,
                    signedAt1: contract.objectData.signedAt1 || null,
                    signedAt2: contract.objectData.signedAt2 || null,
                    relatedItems: contract.objectData.relatedItems || [],
                    billingCycle: contract.objectData.billingCycle || 'monthly',
                    isRecurring: contract.objectData.isRecurring || false
                };
            }
            return {
                customer: '',
                title: '',
                description: '',
                startDate: '',
                endDate: '',
                terms: '',
                value: 0,
                status: 'draft',
                type: 'service',
                scope: '',
                paymentTerms: '',
                renewalTerms: '',
                contractNumber: generateContractNumber(),
                customerSignature: null,
                signedAt1: null,
                signedAt2: null,
                relatedItems: [],
                billingCycle: 'monthly',
                isRecurring: false
            };
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [customers, setCustomers] = React.useState([]);
        const [customerDetails, setCustomerDetails] = React.useState(null);
        const [recurringItems, setRecurringItems] = React.useState([]);
        const [showItemSelection, setShowItemSelection] = React.useState(false);
        const [selectedItems, setSelectedItems] = React.useState([]);

        React.useEffect(() => {
            fetchCustomers();
            fetchRecurringItems();
        }, []);

        React.useEffect(() => {
            console.log('Contract prop changed:', contract); // Debug log
            // Update form data when contract prop changes
            if (contract?.objectData) {
                console.log('Loading contract data into form:', contract.objectData); // Debug log
                setFormData({
                    customer: contract.objectData.customer || '',
                    title: contract.objectData.title || '',
                    description: contract.objectData.description || '',
                    startDate: contract.objectData.startDate || '',
                    endDate: contract.objectData.endDate || '',
                    terms: contract.objectData.terms || '',
                    value: contract.objectData.value || 0,
                    status: contract.objectData.status || 'draft',
                    type: contract.objectData.type || 'service',
                    scope: contract.objectData.scope || '',
                    paymentTerms: contract.objectData.paymentTerms || '',
                    renewalTerms: contract.objectData.renewalTerms || '',
                    contractNumber: contract.objectData.contractNumber || generateContractNumber(),
                    customerSignature: contract.objectData.customerSignature || null,
                    signedAt1: contract.objectData.signedAt1 || null,
                    signedAt2: contract.objectData.signedAt2 || null,
                    relatedItems: contract.objectData.relatedItems || [],
                    billingCycle: contract.objectData.billingCycle || 'monthly',
                    isRecurring: contract.objectData.isRecurring || false
                });
            } else {
                console.log('No contract data, setting defaults'); // Debug log
                // Set default dates for new contracts
                const today = new Date().toISOString().split('T')[0];
                const endDate = new Date();
                endDate.setFullYear(endDate.getFullYear() + 1);
                
                setFormData({
                    customer: '',
                    title: '',
                    description: '',
                    startDate: today,
                    endDate: endDate.toISOString().split('T')[0],
                    terms: '',
                    value: 0,
                    status: 'draft',
                    type: 'service',
                    scope: '',
                    paymentTerms: '',
                    renewalTerms: '',
                    contractNumber: generateContractNumber(),
                    customerSignature: null,
                    signedAt1: null,
                    signedAt2: null,
                    relatedItems: [],
                    billingCycle: 'monthly',
                    isRecurring: false
                });
            }
        }, [contract]);

        React.useEffect(() => {
            if (formData.customer) {
                fetchCustomerDetails(formData.customer);
            }
        }, [formData.customer]);

        // Initialize selected items from contract data
        React.useEffect(() => {
            if (contract?.objectData?.relatedItems && recurringItems.length > 0) {
                const itemsToSelect = [];
                contract.objectData.relatedItems.forEach(relatedItem => {
                    const foundItem = recurringItems.find(item => item.objectId === relatedItem.itemId);
                    if (foundItem) {
                        itemsToSelect.push({
                            ...foundItem,
                            quantity: relatedItem.quantity || 1
                        });
                    }
                });
                setSelectedItems(itemsToSelect);
            }
        }, [recurringItems, contract?.objectData?.relatedItems]);

        const fetchCustomers = async () => {
            try {
                const response = await window.apiWrapper.listObjects('customer', 100, true);
                setCustomers(response.items);
            } catch (error) {
                console.error('Error fetching customers:', error);
            }
        };

        const fetchRecurringItems = async () => {
            try {
                const response = await window.apiWrapper.listObjects('item', 100, true);
                // Filter for recurring items and active items
                const items = response.items.filter(item => 
                    item.objectData.isRecurring && item.objectData.isActive !== false
                );
                setRecurringItems(items);
            } catch (error) {
                console.error('Error fetching recurring items:', error);
            }
        };

        const fetchCustomerDetails = async (customerId) => {
            try {
                const response = await window.apiWrapper.getObject('customer', customerId);
                setCustomerDetails(response.objectData);
            } catch (error) {
                console.error('Error fetching customer details:', error);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type, checked } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'checkbox' ? checked : 
                       (type === 'number' ? parseFloat(value) || 0 : value)
            }));

            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        const handleItemSelection = (item, selected) => {
            if (selected) {
                setSelectedItems(prev => [...prev, { ...item, quantity: 1 }]);
            } else {
                setSelectedItems(prev => prev.filter(i => i.objectId !== item.objectId));
            }
        };

        const handleItemQuantityChange = (itemId, quantity) => {
            setSelectedItems(prev => 
                prev.map(item => 
                    item.objectId === itemId 
                        ? { ...item, quantity: parseInt(quantity) || 1 } 
                        : item
                )
            );
        };

        const calculateContractValue = () => {
            let totalValue = 0;
            
            // Add value from selected recurring items
            selectedItems.forEach(item => {
                const itemPrice = item.objectData.price || 0;
                const quantity = item.quantity || 1;
                const monthsInCycle = getMonthsInCycle(formData.billingCycle);
                
                // Calculate months between start and end dates
                const startDate = new Date(formData.startDate);
                const endDate = new Date(formData.endDate);
                const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                                  (endDate.getMonth() - startDate.getMonth());
                
                // Calculate billing cycles in contract duration
                const billingCycles = Math.ceil(monthsDiff / monthsInCycle);
                
                // Add to total value
                totalValue += itemPrice * quantity * billingCycles;
            });
            
            return totalValue + (formData.value || 0);
        };
        
        const getMonthsInCycle = (cycle) => {
            switch (cycle) {
                case 'monthly': return 1;
                case 'quarterly': return 3;
                case 'biannually': return 6;
                case 'annually': return 12;
                default: return 1;
            }
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.customer) {
                newErrors.customer = 'Customer is required';
            }
            if (!formData.title) {
                newErrors.title = 'Contract title is required';
            }
            if (!formData.startDate) {
                newErrors.startDate = 'Start date is required';
            }
            if (!formData.endDate) {
                newErrors.endDate = 'End date is required';
            }
            if (formData.startDate && formData.endDate && new Date(formData.endDate) <= new Date(formData.startDate)) {
                newErrors.endDate = 'End date must be after start date';
            }
            if (!formData.terms) {
                newErrors.terms = 'Contract terms are required';
            }
            
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                
                // Format related items for storage
                const relatedItems = selectedItems.map(item => ({
                    itemId: item.objectId,
                    name: item.objectData.name,
                    price: item.objectData.price,
                    quantity: item.quantity || 1,
                    recurringPeriod: item.objectData.recurringPeriod
                }));
                
                // Calculate contract value if using recurring items
                const calculatedValue = formData.isRecurring ? calculateContractValue() : formData.value;
                
                const contractData = {
                    ...formData,
                    value: calculatedValue,
                    relatedItems,
                    updatedAt: new Date().toISOString()
                };

                // Add signature date if contract is signed
                if (formData.status === 'signed' && !formData.signedAt1) {
                    contractData.signedAt1 = new Date().toISOString();
                }

                let savedContract;
                if (contract?.objectId) {
                    savedContract = await trickleUpdateObject('contract', contract.objectId, contractData);
                } else {
                    savedContract = await trickleCreateObject('contract', contractData);
                }

                if (onSave) {
                    onSubmit(savedContract);
                }
            } catch (error) {
                console.error('Error saving contract:', error);
                setErrors({ submit: 'Failed to save contract' });
            } finally {
                setLoading(false);
            }
        };

        return (
            <form data-name="contract-form" onSubmit={handleSubmit} className="contract-form">
                <div className="grid grid-cols-1 gap-6">
                    <div className="flex justify-between items-center">
                        <div className="text-xl font-bold text-gray-700">
                            Contract #{formData.contractNumber}
                        </div>
                        <div>
                            <Button
                                type="button"
                                variant="secondary"
                                icon="fas fa-random"
                                onClick={() => setFormData(prev => ({
                                    ...prev,
                                    contractNumber: generateContractNumber()
                                }))}
                                className="text-sm"
                            >
                                Regenerate Number
                            </Button>
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Customer <span className="text-red-500">*</span>
                        </label>
                        <select
                            name="customer"
                            value={formData.customer}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="">Select Customer</option>
                            {customers.map(customer => (
                                <option key={customer.objectId} value={customer.objectId}>
                                    {customer.objectData.name}
                                </option>
                            ))}
                        </select>
                        {errors.customer && (
                            <p className="mt-1 text-sm text-red-600">{errors.customer}</p>
                        )}
                    </div>

                    {customerDetails && (
                        <div className="bg-gray-50 p-4 rounded-md">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h3>
                            <div className="text-sm">
                                <p><span className="font-medium">Name:</span> {customerDetails.name}</p>
                                {customerDetails.company && <p><span className="font-medium">Company:</span> {customerDetails.company}</p>}
                                {customerDetails.email && <p><span className="font-medium">Email:</span> {customerDetails.email}</p>}
                                {customerDetails.phone && <p><span className="font-medium">Phone:</span> {customerDetails.phone}</p>}
                            </div>
                        </div>
                    )}

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Contract Title <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                name="title"
                                value={formData.title}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="e.g. Web Development Services"
                            />
                            {errors.title && (
                                <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Contract Type
                            </label>
                            <select
                                name="type"
                                value={formData.type}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="service">Service Contract</option>
                                <option value="product">Product Contract</option>
                                <option value="licensing">Licensing Agreement</option>
                                <option value="partnership">Partnership Agreement</option>
                                <option value="employment">Employment Contract</option>
                                <option value="maintenance">Maintenance Contract</option>
                                <option value="digital-marketing">Digital Marketing Contract</option>
                            </select>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Start Date <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="date"
                                name="startDate"
                                value={formData.startDate}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.startDate && (
                                <p className="mt-1 text-sm text-red-600">{errors.startDate}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                End Date <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="date"
                                name="endDate"
                                value={formData.endDate}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.endDate && (
                                <p className="mt-1 text-sm text-red-600">{errors.endDate}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Status
                            </label>
                            <select
                                name="status"
                                value={formData.status}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="draft">Draft</option>
                                <option value="pending">Pending Signature</option>
                                <option value="signed">Signed</option>
                                <option value="active">Active</option>
                                <option value="expired">Expired</option>
                                <option value="terminated">Terminated</option>
                            </select>
                        </div>
                    </div>

                    <div className="border-t pt-4 mt-2">
                        <div className="flex items-center mb-4">
                            <input
                                type="checkbox"
                                id="isRecurring"
                                name="isRecurring"
                                checked={formData.isRecurring}
                                onChange={handleInputChange}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="isRecurring" className="ml-2 block text-sm text-gray-900">
                                This is a recurring service contract
                            </label>
                        </div>

                        {formData.isRecurring && (
                            <div className="space-y-4 mb-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">
                                        Billing Cycle
                                    </label>
                                    <select
                                        name="billingCycle"
                                        value={formData.billingCycle}
                                        onChange={handleInputChange}
                                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    >
                                        <option value="monthly">Monthly</option>
                                        <option value="quarterly">Quarterly</option>
                                        <option value="biannually">Bi-annually</option>
                                        <option value="annually">Annually</option>
                                    </select>
                                </div>

                                <div>
                                    <div className="flex justify-between items-center mb-2">
                                        <label className="block text-sm font-medium text-gray-700">
                                            Recurring Services
                                        </label>
                                        <button
                                            type="button"
                                            onClick={() => setShowItemSelection(!showItemSelection)}
                                            className="text-blue-600 hover:text-blue-800 text-sm"
                                        >
                                            {showItemSelection ? 'Hide Services' : 'Add Services'}
                                        </button>
                                    </div>

                                    {showItemSelection && (
                                        <div className="bg-gray-50 p-4 rounded-md mb-4">
                                            <h4 className="font-medium text-sm mb-2">Available Recurring Services</h4>
                                            <div className="space-y-2 max-h-60 overflow-y-auto">
                                                {recurringItems.length === 0 ? (
                                                    <p className="text-sm text-gray-500">No recurring services available</p>
                                                ) : (
                                                    recurringItems.map(item => {
                                                        const isSelected = selectedItems.some(i => i.objectId === item.objectId);
                                                        return (
                                                            <div 
                                                                key={item.objectId} 
                                                                className="flex items-center justify-between p-2 hover:bg-gray-100 rounded"
                                                            >
                                                                <div className="flex items-center">
                                                                    <input
                                                                        type="checkbox"
                                                                        id={`item-${item.objectId}`}
                                                                        checked={isSelected}
                                                                        onChange={(e) => handleItemSelection(item, e.target.checked)}
                                                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                                    />
                                                                    <label htmlFor={`item-${item.objectId}`} className="ml-2 block text-sm text-gray-900">
                                                                        {item.objectData.name}
                                                                    </label>
                                                                </div>
                                                                <div className="text-sm text-gray-600">
                                                                    {formatCurrency(item.objectData.price)} / {item.objectData.recurringPeriod}
                                                                </div>
                                                            </div>
                                                        );
                                                    })
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {selectedItems.length > 0 && (
                                        <div className="border rounded-md">
                                            <h4 className="font-medium text-sm p-3 bg-gray-50 border-b">Selected Services</h4>
                                            <div className="divide-y">
                                                {selectedItems.map(item => (
                                                    <div key={item.objectId} className="p-3 flex items-center justify-between">
                                                        <div>
                                                            <p className="font-medium text-sm">{item.objectData.name}</p>
                                                            <p className="text-xs text-gray-500">
                                                                {formatCurrency(item.objectData.price)} / {item.objectData.recurringPeriod}
                                                            </p>
                                                        </div>
                                                        <div className="flex items-center space-x-4">
                                                            <div className="flex items-center">
                                                                <label className="text-xs text-gray-500 mr-2">Qty:</label>
                                                                <input
                                                                    type="number"
                                                                    min="1"
                                                                    value={item.quantity || 1}
                                                                    onChange={(e) => handleItemQuantityChange(item.objectId, e.target.value)}
                                                                    className="w-16 text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                                                />
                                                            </div>
                                                            <button
                                                                type="button"
                                                                onClick={() => handleItemSelection(item, false)}
                                                                className="text-red-600 hover:text-red-800"
                                                            >
                                                                <i className="fas fa-times"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            <div className="p-3 bg-gray-50 border-t flex justify-between">
                                                <span className="font-medium">Total Contract Value:</span>
                                                <span className="font-bold">{formatCurrency(calculateContractValue())}</span>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {!formData.isRecurring && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700">
                                    Contract Value <span className="text-red-500">*</span>
                                </label>
                                <div className="mt-1 relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <span className="text-gray-500 sm:text-sm">₹</span>
                                    </div>
                                    <input
                                        type="number"
                                        name="value"
                                        value={formData.value}
                                        onChange={handleInputChange}
                                        min="0"
                                        step="0.01"
                                        className="block w-full pl-7 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    />
                                </div>
                                {errors.value && (
                                    <p className="mt-1 text-sm text-red-600">{errors.value}</p>
                                )}
                            </div>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <textarea
                            name="description"
                            value={formData.description}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Brief description of the contract"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Scope of Work
                        </label>
                        <textarea
                            name="scope"
                            value={formData.scope}
                            onChange={handleInputChange}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Detailed description of the work to be performed"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Payment Terms
                        </label>
                        <textarea
                            name="paymentTerms"
                            value={formData.paymentTerms}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Payment schedule, methods, and conditions"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Terms and Conditions <span className="text-red-500">*</span>
                        </label>
                        <textarea
                            name="terms"
                            value={formData.terms}
                            onChange={handleInputChange}
                            rows={6}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Legal terms and conditions of the contract"
                        />
                        {errors.terms && (
                            <p className="mt-1 text-sm text-red-600">{errors.terms}</p>
                        )}
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Renewal Terms
                        </label>
                        <textarea
                            name="renewalTerms"
                            value={formData.renewalTerms}
                            onChange={handleInputChange}
                            rows={2}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Terms for contract renewal"
                        />
                    </div>

                    {formData.status === 'signed' && (
                        <div className="border p-4 rounded-md bg-gray-50">
                            <h3 className="font-medium text-gray-700 mb-2">Signature Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">Company Representative: Signed</p>
                                    <p className="text-sm text-gray-600">Date: {formatDate(formData.signedAt1 || new Date())}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Customer: {formData.customerSignature ? 'Signed' : 'Not signed'}</p>
                                    {formData.signedAt2 && (
                                        <p className="text-sm text-gray-600">Date: {formatDate(formData.signedAt2)}</p>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {errors.submit && (
                    <div className="mt-4 text-red-600 text-sm">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {contract?.objectId ? 'Update Contract' : 'Create Contract'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('ContractForm component error:', error);
        reportError(error);
        return null;
    }
}