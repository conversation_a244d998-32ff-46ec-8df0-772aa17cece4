function ContractList({ onContractClick, onEdit, onDelete, onPrint }) {
    try {
        const [contracts, setContracts] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [selectedType, setSelectedType] = React.useState('');
        const [customersMap, setCustomersMap] = React.useState({});

        React.useEffect(() => {
            fetchContracts();
            fetchCustomers();
        }, []);

        const fetchContracts = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.getContracts();
                setContracts(response?.items || []);
            } catch (error) {
                console.error('Error fetching contracts:', error);
            } finally {
                setLoading(false);
            }
        };

        const fetchCustomers = async () => {
            try {
                const response = await window.fetchCustomers();
                const customerMap = {};
                response.forEach(customer => {
                    customerMap[customer.id] = customer;
                });
                setCustomersMap(customerMap);
            } catch (error) {
                console.error('Error fetching customers:', error);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters?.status || '');
            setSelectedType(filters?.type || '');
        };

        const filteredContracts = React.useMemo(() => {
            if (!Array.isArray(contracts)) return [];
            return contracts.filter(contract => {
                const customerName = customersMap[contract.objectData.customer]?.name || '';
                
                const matchesSearch = !searchQuery || 
                    contract.objectData.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    (contract.objectData.contractNumber && contract.objectData.contractNumber.toLowerCase().includes(searchQuery.toLowerCase()));
                
                const matchesStatus = !selectedStatus || contract.objectData.status === selectedStatus;
                const matchesType = !selectedType || contract.objectData.type === selectedType;

                return matchesSearch && matchesStatus && matchesType;
            });
        }, [contracts, searchQuery, selectedStatus, selectedType, customersMap]);

        const contractFilters = [
            { id: 'status', label: 'Status', type: 'select', options: [
                { label: 'Draft', value: 'draft' },
                { label: 'Pending', value: 'pending' },
                { label: 'Signed', value: 'signed' },
                { label: 'Expired', value: 'expired' }
            ]},
            { id: 'type', label: 'Type', type: 'select', options: [
                { label: 'Service Contract', value: 'service' },
                { label: 'Product Contract', value: 'product' },
                { label: 'Licensing Agreement', value: 'licensing' },
                { label: 'Partnership Agreement', value: 'partnership' }
            ]}
        ];

        const handleEdit = (contract, e) => {
            e.stopPropagation();
            console.log('Editing contract:', contract); // Debug log
            if (onEdit) onEdit(contract);
        };

        const handleDelete = (contract, e) => {
            e.stopPropagation();
            if (onDelete) onDelete(contract);
        };

        

        const columns = React.useMemo(() => [
            { 
                key: 'contractNumber', 
                title: 'Contract #',
                render: (row) => row.objectData.contractNumber || `#${row.objectId.substring(0, 8)}`
            },
            { key: 'title', title: 'Contract Title' },
            { 
                key: 'customer', 
                title: 'Customer',
                render: (row) => customersMap[row.objectData.customer]?.name || 'Unknown Customer'
            },
            { 
                key: 'value', 
                title: 'Value',
                render: (row) => formatCurrency(row.objectData.value)
            },
            {
                key: 'startDate',
                title: 'Start Date',
                render: (row) => formatDate(row.objectData.startDate)
            },
            {
                key: 'endDate',
                title: 'End Date',
                render: (row) => formatDate(row.objectData.endDate)
            },
            {
                key: 'status',
                title: 'Status',
                render: (row) => (
                    <span className={`contract-status ${row.objectData.status}`}>
                        {row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1)}
                    </span>
                )
            },
            {
                key: 'actions',
                title: 'Actions',
                render: (row) => (
                    <div className="flex space-x-2">
                        <button
                            onClick={(e) => handleEdit(row, e)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Edit Contract"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                                                <button
                            onClick={(e) => { e.stopPropagation(); onPrint(row); }}
                            className="text-green-600 hover:text-green-800"
                            title="Print Contract"
                        >
                            <i className="fas fa-print"></i>
                        </button>
                        <button
                            onClick={(e) => handleDelete(row, e)}
                            className="text-red-600 hover:text-red-800"
                            title="Delete Contract"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                )
            }
        ], [customersMap]);

        return (
            <div data-name="contract-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search contracts..."
                        filters={contractFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredContracts}
                    loading={loading}
                    onRowClick={onContractClick}
                    emptyMessage="No contracts found"
                />
            </div>
        );
    } catch (error) {
        console.error('ContractList component error:', error);
        reportError(error);
        return null;
    }
}
