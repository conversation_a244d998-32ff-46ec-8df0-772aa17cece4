function CustomerDetails({ customer, onEdit, onDelete, onClose }) {
    if (!customer || !customer.objectData) {
        return <div className="p-6">Loading customer details...</div>;
    }
    try {
        const [relatedData, setRelatedData] = React.useState({
            quotations: [],
            invoices: [],
            contracts: []
        });
        const [loading, setLoading] = React.useState(true);
        const [activeTab, setActiveTab] = React.useState('details');

        React.useEffect(() => {
            if (customer && customer.objectId) {
                fetchRelatedData();
            }
        }, [customer]);

        const fetchRelatedData = async () => {
            try {
                setLoading(true);
                const [quotationsRes, invoicesRes, contractsRes] = await Promise.all([
                    window.apiWrapper.listObjects('quotation', 100, true),
                    window.apiWrapper.listObjects('invoice', 100, true),
                    window.apiWrapper.listObjects('contract', 100, true)
                ]);

                setRelatedData({
                    quotations: quotationsRes.items?.filter(q => q.objectData.customer === customer.objectId) || [],
                    invoices: invoicesRes.items?.filter(i => i.objectData.customer === customer.objectId) || [],
                    contracts: contractsRes.items?.filter(c => c.objectData.customer === customer.objectId) || []
                });
            } catch (error) {
                console.error('Error fetching related data:', error);
            } finally {
                setLoading(false);
            }
        };

        const renderTabContent = () => {
            switch (activeTab) {
                case 'quotations':
                    return (
                        <div className="space-y-4">
                            {relatedData.quotations.length === 0 ? (
                                <p className="text-gray-500">No quotations found</p>
                            ) : (
                                relatedData.quotations.map(quotation => (
                                    <div key={quotation.objectId} className="border rounded-lg p-4">
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h4 className="font-medium">#{quotation.objectData.quotationNumber}</h4>
                                                <p className="text-sm text-gray-600">
                                                    {formatCurrency(quotation.objectData.total)}
                                                </p>
                                            </div>
                                            <span className={`px-2 py-1 text-xs rounded-full ${
                                                quotation.objectData.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                                quotation.objectData.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                                'bg-yellow-100 text-yellow-800'
                                            }`}>
                                                {quotation.objectData.status}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    );
                case 'invoices':
                    return (
                        <div className="space-y-4">
                            {relatedData.invoices.length === 0 ? (
                                <p className="text-gray-500">No invoices found</p>
                            ) : (
                                relatedData.invoices.map(invoice => (
                                    <div key={invoice.objectId} className="border rounded-lg p-4">
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h4 className="font-medium">#{invoice.objectData.invoiceNumber}</h4>
                                                <p className="text-sm text-gray-600">
                                                    {formatCurrency(invoice.objectData.total)}
                                                </p>
                                            </div>
                                            <span className={`px-2 py-1 text-xs rounded-full ${
                                                invoice.objectData.status === 'paid' ? 'bg-green-100 text-green-800' :
                                                invoice.objectData.status === 'overdue' ? 'bg-red-100 text-red-800' :
                                                'bg-yellow-100 text-yellow-800'
                                            }`}>
                                                {invoice.objectData.status}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    );
                case 'contracts':
                    return (
                        <div className="space-y-4">
                            {relatedData.contracts.length === 0 ? (
                                <p className="text-gray-500">No contracts found</p>
                            ) : (
                                relatedData.contracts.map(contract => (
                                    <div key={contract.objectId} className="border rounded-lg p-4">
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <h4 className="font-medium">{contract.objectData.title}</h4>
                                                <p className="text-sm text-gray-600">
                                                    {formatCurrency(contract.objectData.value)}
                                                </p>
                                            </div>
                                            <span className={`px-2 py-1 text-xs rounded-full ${
                                                contract.objectData.status === 'active' ? 'bg-green-100 text-green-800' :
                                                contract.objectData.status === 'expired' ? 'bg-red-100 text-red-800' :
                                                'bg-yellow-100 text-yellow-800'
                                            }`}>
                                                {contract.objectData.status}
                                            </span>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    );
                default:
                    return (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <span className="text-gray-500">Email</span>
                                    <p className="font-medium">{customer.objectData.email || 'Not provided'}</p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Phone</span>
                                    <p className="font-medium">{customer.objectData.phone || 'Not provided'}</p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Company</span>
                                    <p className="font-medium">{customer.objectData.company || 'Individual'}</p>
                                </div>
                                <div>
                                    <span className="text-gray-500">Customer Type</span>
                                    <p className="font-medium">
                                        {customer.objectData.type ? 
                                            customer.objectData.type.charAt(0).toUpperCase() + customer.objectData.type.slice(1) : 
                                            'Individual'
                                        }
                                    </p>
                                </div>
                            </div>
                            {customer.objectData.address && (
                                <div>
                                    <span className="text-gray-500">Address</span>
                                    <p className="font-medium">{customer.objectData.address}</p>
                                </div>
                            )}
                            {customer.objectData.notes && (
                                <div>
                                    <span className="text-gray-500">Notes</span>
                                    <p className="font-medium whitespace-pre-line">{customer.objectData.notes}</p>
                                </div>
                            )}
                        </div>
                    );
            }
        };

        return (
            <div data-name="customer-details" data-file="components/customers/CustomerDetails.js">
                <div className="flex items-start justify-between mb-6">
                    <div className="flex items-center">
                        <div className="customer-avatar">
                            {customer.objectData.name && customer.objectData.name.length > 0 ? 
                                customer.objectData.name[0].toUpperCase() : 
                                'N'
                            }
                        </div>
                        <div className="ml-4">
                            <h2 className="text-2xl font-bold">{customer.objectData.name || 'Unknown Customer'}</h2>
                            <p className="text-gray-600">{customer.objectData.company || 'Individual Customer'}</p>
                        </div>
                    </div>
                    <div className="flex space-x-4">
                        <Button variant="secondary" icon="fas fa-edit" onClick={onEdit}>
                            Edit
                        </Button>
                        <Button variant="danger" icon="fas fa-trash" onClick={onDelete}>
                            Delete
                        </Button>
                        <Button variant="secondary" icon="fas fa-times" onClick={onClose}>
                            Close
                        </Button>
                    </div>
                </div>

                <div className="mb-6 border-b border-gray-200">
                    <nav className="flex -mb-px">
                        {['details', 'quotations', 'invoices', 'contracts'].map(tab => (
                            <button
                                key={tab}
                                className={`mr-8 py-4 text-sm font-medium border-b-2 ${
                                    activeTab === tab
                                        ? 'border-blue-500 text-blue-600'
                                        : 'border-transparent text-gray-500 hover:text-gray-700'
                                }`}
                                onClick={() => setActiveTab(tab)}
                            >
                                {tab.charAt(0).toUpperCase() + tab.slice(1)}
                                {tab !== 'details' && (
                                    <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                                        {relatedData[tab]?.length || 0}
                                    </span>
                                )}
                            </button>
                        ))}
                    </nav>
                </div>

                {loading ? (
                    <div className="flex justify-center items-center h-32">
                        <i className="fas fa-spinner fa-spin text-blue-500"></i>
                    </div>
                ) : (
                    renderTabContent()
                )}
            </div>
        );
    } catch (error) {
        console.error('CustomerDetails component error:', error);
        reportError(error);
        return null;
    }
}