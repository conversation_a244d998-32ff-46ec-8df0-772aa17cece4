function CustomerForm({ customer, onSubmit, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            name: customer?.objectData?.name || '',
            email: customer?.objectData?.email || '',
            phone: customer?.objectData?.phone || '',
            company: customer?.objectData?.company || '',
            address: customer?.objectData?.address || '',
            type: customer?.objectData?.type || 'individual',
            notes: customer?.objectData?.notes || ''
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);

        const validateForm = () => {
            const newErrors = {};
            if (!validateRequired(formData.name)) {
                newErrors.name = 'Name is required';
            }
            if (!validateRequired(formData.email)) {
                newErrors.email = 'Email is required';
            } else if (!isEmailValid(formData.email)) {
                newErrors.email = 'Invalid email format';
            }
            if (formData.phone && !isPhoneValid(formData.phone)) {
                newErrors.phone = 'Invalid phone format';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const customerData = {
                    ...formData,
                    updatedAt: new Date().toISOString()
                };

                if (customer?.objectId) {
                    await trickleUpdateObject('customer', customer.objectId, customerData);
                } else {
                    await trickleCreateObject('customer', customerData);
                }

                onSubmit();
            } catch (error) {
                console.error('Error saving customer:', error);
                setErrors({ submit: 'Failed to save customer' });
            } finally {
                setLoading(false);
            }
        };

        const handleChange = (e) => {
            const { name, value } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: value
            }));
            if (errors[name]) {
                setErrors(prev => ({
                    ...prev,
                    [name]: ''
                }));
            }
        };

        return (
            <form data-name="customer-form" onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                        label="Name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        error={errors.name}
                        required
                    />
                    <Input
                        label="Email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        error={errors.email}
                        required
                    />
                    <Input
                        label="Phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        error={errors.phone}
                    />
                    <Input
                        label="Company"
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                    />
                    <div className="md:col-span-2">
                        <Input
                            label="Address"
                            name="address"
                            value={formData.address}
                            onChange={handleChange}
                        />
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Customer Type
                        </label>
                        <select
                            name="type"
                            value={formData.type}
                            onChange={handleChange}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="individual">Individual</option>
                            <option value="business">Business</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>
                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Notes
                        </label>
                        <textarea
                            name="notes"
                            value={formData.notes}
                            onChange={handleChange}
                            rows={4}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>
                </div>

                {errors.submit && (
                    <div className="text-red-600 text-sm mt-2">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {customer?.objectId ? 'Update Customer' : 'Create Customer'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('CustomerForm component error:', error);
        reportError(error);
        return null;
    }
}
