function CustomerList({ onCustomerClick }) {
    try {
        const [customers, setCustomers] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedType, setSelectedType] = React.useState('');
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [customerToDelete, setCustomerToDelete] = React.useState(null);


        React.useEffect(() => {
            fetchCustomersList();
        }, []);

        const fetchCustomersList = async () => {
            try {
                setLoading(true);
                const response = await window.fetchCustomers();
                setCustomers(response);
            } catch (error) {
                console.error('Error fetching customers:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load customers'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedType(filters?.type || '');
        };

        const handleDeleteCustomer = (customer, e) => {
            e.stopPropagation();
            setCustomerToDelete(customer);
            setShowDeleteConfirm(true);
        };

        const confirmDelete = async () => {
            try {
                await window.deleteCustomer(customerToDelete.id);
                setCustomers(prev => prev.filter(c => c.id !== customerToDelete.id));
                setNotification({
                    type: 'success',
                    message: 'Customer deleted successfully'
                });
            } catch (error) {
                console.error('Error deleting customer:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete customer'
                });
            } finally {
                setShowDeleteConfirm(false);
                setCustomerToDelete(null);
            }
        };

        const filteredCustomers = React.useMemo(() => {
            return customers.filter(customer => {
                const matchesSearch = !searchQuery || 
                    (customer.name && customer.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (customer.email && customer.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (customer.company && customer.company.toLowerCase().includes(searchQuery.toLowerCase()));
                const matchesType = !selectedType || (customer.type && customer.type === selectedType);
                return matchesSearch && matchesType;
            });
        }, [customers, searchQuery, selectedType]);

        const customerFilters = [
            { 
                id: 'type', 
                label: 'Customer Type', 
                type: 'select', 
                options: [
                    { label: 'Individual', value: 'individual' },
                    { label: 'Business', value: 'business' },
                    { label: 'Enterprise', value: 'enterprise' }
                ]
            }
        ];

        

        return (
            <div data-name="customer-list" data-file="components/customers/CustomerList.js">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search customers..."
                        filters={customerFilters}
                    />
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {loading ? (
                        <p>Loading...</p>
                    ) : filteredCustomers.length > 0 ? (
                        filteredCustomers.map(customer => (
                            <div key={customer.id} onClick={() => onCustomerClick({ objectData: customer })} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow cursor-pointer">
                                <h3 className="text-lg font-semibold mb-2">{customer.name}</h3>
                                <p className="text-gray-600 mb-2">{customer.email}</p>
                                <p className="text-gray-600 mb-4">{customer.company}</p>
                                <div className="flex justify-between items-center">
                                    <span className={`px-2 py-1 text-xs rounded-full ${
                                        customer.type === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                                        customer.type === 'business' ? 'bg-blue-100 text-blue-800' :
                                        'bg-gray-100 text-gray-800'
                                    }`}>
                                        {customer.type ? customer.type.charAt(0).toUpperCase() + customer.type.slice(1) : 'Individual'}
                                    </span>
                                    <button
                                        onClick={(e) => handleDeleteCustomer(customer, e)}
                                        className="text-red-600 hover:text-red-800"
                                        title="Delete Customer"
                                    >
                                        <i className="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        ))
                    ) : (
                        <p>No customers found</p>
                    )}
                </div>

                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete "{customerToDelete?.name}"? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('CustomerList component error:', error);
        reportError(error);
        return null;
    }
}