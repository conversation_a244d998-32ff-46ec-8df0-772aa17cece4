function DocumentViewer({ type, data, companyInfo, settings, onEdit, onClose }) {
    try {
        return (
            <div data-name="document-viewer" className="bg-white rounded-lg shadow">
                <div className="p-6 template-container">
                    {type === 'invoice' && (
                        <InvoiceTemplate 
                            data={data || {}}
                            companyInfo={companyInfo}
                            settings={settings || companyInfo}
                        />
                    )}
                    {type === 'quotation' && (
                        <QuotationTemplate 
                            quotation={data || {}}
                            companyInfo={companyInfo}
                            settings={settings || companyInfo}
                        />
                    )}
                    {type === 'contract' && (
                        <ContractTemplate 
                            contract={data || {}}
                            companyInfo={companyInfo}
                            settings={settings || companyInfo}
                        />
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('DocumentViewer error:', error);
        return <div>Error loading document viewer</div>;
    }
}
