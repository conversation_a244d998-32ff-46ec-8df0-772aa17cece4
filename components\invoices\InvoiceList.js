function InvoiceList({ onInvoiceClick, onEdit, onDelete }) {
    try {
        const [invoices, setInvoices] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchTerm, setSearchTerm] = React.useState('');
        const [statusFilter, setStatusFilter] = React.useState('all');

        React.useEffect(() => {
            fetchInvoices();
        }, []);

        const fetchInvoices = async () => {
            try {
                setLoading(true);
                const response = await window.fetchInvoices();
                setInvoices(response || []);
            } catch (error) {
                console.error('Error fetching invoices:', error);
                setInvoices([]); // Set empty array on error
            } finally {
                setLoading(false);
            }
        };

        const filteredInvoices = invoices.filter(invoice => {
            const data = invoice.objectData;
            const matchesSearch = data.invoiceNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                data.customer?.toLowerCase().includes(searchTerm.toLowerCase());
            const matchesStatus = statusFilter === 'all' || data.status === statusFilter;
            return matchesSearch && matchesStatus;
        });

        const getStatusBadge = (status) => {
            const colors = {
                draft: 'bg-gray-100 text-gray-800',
                sent: 'bg-blue-100 text-blue-800',
                paid: 'bg-green-100 text-green-800',
                overdue: 'bg-red-100 text-red-800',
                cancelled: 'bg-gray-100 text-gray-600'
            };
            return `px-2 py-1 rounded-full text-xs font-medium ${colors[status] || colors.draft}`;
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-32">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="invoice-list" className="space-y-4">
                <div className="flex gap-4 mb-6">
                    <div className="flex-1">
                        <SearchBar
                            value={searchTerm}
                            onChange={setSearchTerm}
                            placeholder="Search invoices..."
                        />
                    </div>
                    <select
                        value={statusFilter}
                        onChange={(e) => setStatusFilter(e.target.value)}
                        className="rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                        <option value="all">All Status</option>
                        <option value="draft">Draft</option>
                        <option value="sent">Sent</option>
                        <option value="paid">Paid</option>
                        <option value="overdue">Overdue</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                </div>

                <div className="bg-white rounded-lg shadow overflow-hidden">
                    <Table
                        columns={[
                            { key: 'invoiceNumber', label: 'Invoice #' },
                            { key: 'customer', label: 'Customer' },
                            { key: 'total', label: 'Amount' },
                            { key: 'status', label: 'Status' },
                            { key: 'dueDate', label: 'Due Date' },
                            { key: 'actions', label: 'Actions' }
                        ]}
                        data={filteredInvoices.map(invoice => ({
                            id: invoice.objectId,
                            invoiceNumber: invoice.objectData.invoiceNumber,
                            customer: invoice.objectData.customer || 'Unknown',
                            total: formatCurrency(invoice.objectData.total || 0),
                            status: (
                                <span className={getStatusBadge(invoice.objectData.status)}>
                                    {(invoice.objectData.status || 'draft').toUpperCase()}
                                </span>
                            ),
                            dueDate: new Date(invoice.objectData.dueDate).toLocaleDateString(),
                            actions: (
                                <div className="flex space-x-2">
                                    <button
                                        onClick={() => onInvoiceClick(invoice)}
                                        className="text-blue-600 hover:text-blue-900"
                                        title="View"
                                    >
                                        <i className="fas fa-eye"></i>
                                    </button>
                                    <button
                                        onClick={() => onEdit(invoice)}
                                        className="text-green-600 hover:text-green-900"
                                        title="Edit"
                                    >
                                        <i className="fas fa-edit"></i>
                                    </button>
                                    <button
                                        onClick={() => onDelete(invoice)}
                                        className="text-red-600 hover:text-red-900"
                                        title="Delete"
                                    >
                                        <i className="fas fa-trash"></i>
                                    </button>
                                </div>
                            )
                        }))}
                        onRowClick={(row) => {
                            const invoice = invoices.find(inv => inv.objectId === row.id);
                            if (invoice) onInvoiceClick(invoice);
                        }}
                    />
                </div>

                {filteredInvoices.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        No invoices found
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('InvoiceList component error:', error);
        reportError(error);
        return null;
    }
}

window.InvoiceList = InvoiceList;