function ItemList({ onItemClick }) {
    try {
        const [items, setItems] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedCategory, setSelectedCategory] = React.useState('');
        const [categories, setCategories] = React.useState([]);
        const [showInactive, setShowInactive] = React.useState(false);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [itemToDelete, setItemToDelete] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const tableRef = React.useRef(null);

        React.useEffect(() => {
            fetchItems();
            // fetchCategories(); // No backend API for categories yet
        }, []);

        const fetchItems = async () => {
            try {
                setLoading(true);
                const response = await window.fetchItems();
                setItems(response);
            } catch (error) {
                console.error('Error fetching items:', error);
            } finally {
                setLoading(false);
            }
        };

        // const fetchCategories = async () => {
        //     try {
        //         const response = await trickleListObjects('item_category', 100, true);
        //         setCategories(response.items);
        //     } catch (error) {
        //         console.error('Error fetching categories:', error);
        //     }
        // };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedCategory(filters?.category || '');
            setShowInactive(filters?.showInactive || false);
        };

        const getCategoryName = (categoryId) => {
            const category = categories.find(cat => cat.objectId === categoryId);
            return category ? category.objectData.name : 'Uncategorized';
        };

        const handlePrintItems = () => {
            if (tableRef.current) {
                printFormattedTable(tableRef.current, 'Items List');
            }
        };

        const handleDeleteItem = (item, e) => {
            e.stopPropagation(); // Prevent row click
            setItemToDelete(item);
            setShowDeleteConfirm(true);
        };

        const confirmDeleteItem = async () => {
            if (!itemToDelete) return;
            
            try {
                await trickleDeleteObject('item', itemToDelete.objectId);
                setNotification({
                    type: 'success',
                    message: 'Item deleted successfully'
                });
                
                // Remove from local state
                setItems(items.filter(item => item.objectId !== itemToDelete.objectId));
            } catch (error) {
                console.error('Error deleting item:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete item'
                });
            } finally {
                setShowDeleteConfirm(false);
                setItemToDelete(null);
            }
        };

        const cancelDeleteItem = () => {
            setShowDeleteConfirm(false);
            setItemToDelete(null);
        };

        const filteredItems = React.useMemo(() => {
            return items.filter(item => {
                const matchesSearch = !searchQuery || 
                    (item.name && item.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (item.sku && item.sku.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()));
                const matchesCategory = !selectedCategory || item.category === selectedCategory;
                const matchesStatus = showInactive || item.is_active !== false;
                return matchesSearch && matchesCategory && matchesStatus;
            });
        }, [items, searchQuery, selectedCategory, showInactive]);

        const itemFilters = [
            { 
                id: 'category', 
                label: 'Category', 
                type: 'select', 
                options: [
                    { label: 'All Categories', value: '' }
                    // ...categories.map(category => ({
                    //     label: category.name,
                    //     value: category.id
                    // }))
                ]
            },
            { 
                id: 'showInactive', 
                label: 'Show Inactive Items', 
                type: 'checkbox' 
            }
        ];

        const columns = [
            { key: 'name', label: 'Item Name' },
            { 
                key: 'category', 
                label: 'Category',
                render: (row) => row.category || 'Uncategorized'
            },
            { 
                key: 'type', 
                label: 'Type',
                render: (row) => {
                    const type = row.item_type || 'product';
                    const isRecurring = row.is_recurring;
                    return (
                        <div>
                            <span className="capitalize">{type}</span>
                            {isRecurring && (
                                <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                    Recurring
                                </span>
                            )}
                        </div>
                    );
                }
            },
            { key: 'sku', label: 'SKU' },
            { 
                key: 'price', 
                label: 'Price',
                render: (row) => formatCurrency(row.price)
            },
            { 
                key: 'stockQuantity', 
                label: 'Stock',
                render: (row) => {
                    if (row.item_type === 'service') {
                        return 'N/A';
                    }
                    return `${row.stock_quantity || 0} ${row.unit || 'piece'}`;
                }
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`px-2 py-1 text-xs rounded-full ${row.is_active !== false ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {row.is_active !== false ? 'Active' : 'Inactive'}
                    </span>
                )
            },
            {
                key: 'actions',
                label: 'Actions',
                render: (row) => (
                    <button
                        onClick={(e) => handleDeleteItem(row, e)}
                        className="text-red-600 hover:text-red-800"
                        title="Delete Item"
                    >
                        <i className="fas fa-trash"></i>
                    </button>
                )
            }
        ];

        return (
            <div data-name="item-list">
                <div className="mb-6 flex justify-between items-center">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search items..."
                        filters={itemFilters}
                    />
                    <Button
                        variant="secondary"
                        icon="fas fa-print"
                        onClick={handlePrintItems}
                    >
                        Print List
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                <div ref={tableRef}>
                    <Table
                        columns={columns}
                        data={filteredItems}
                        loading={loading}
                        onRowClick={onItemClick}
                        emptyMessage="No items found"
                    />
                </div>

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete "{itemToDelete?.objectData?.name}"? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={cancelDeleteItem}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDeleteItem}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('ItemList component error:', error);
        reportError(error);
        return null;
    }
}
