import { useAuth } from '../auth/AuthContext';
import LogoutButton from '../auth/LogoutButton';

export default function Header() {
    try {
        const { isAuthenticated } = useAuth();
        const [notifications, setNotifications] = React.useState([]);
        const [showNotifications, setShowNotifications] = React.useState(false);
        const [searchQuery, setSearchQuery] = React.useState('');

        const handleSearch = (e) => {
            setSearchQuery(e.target.value);
            // You may want to implement search API calls here
        };

        const toggleNotifications = () => {
            setShowNotifications(!showNotifications);
        };

        return (
            <header className="bg-white shadow-sm sticky top-0 z-10">
                <div className="container mx-auto px-6 py-4 flex justify-between items-center">
                    <div className="flex-1 max-w-xl">
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search..."
                                value={searchQuery}
                                onChange={handleSearch}
                                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"
                            />
                            <i className="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                        </div>
                    </div>

                    <div className="flex items-center space-x-4">
                        <button
                            onClick={toggleNotifications} 
                            className="p-2 text-gray-600 hover:text-gray-900 relative"
                        >
                            <i className="fas fa-bell"></i>
                            {notifications.length > 0 && (
                                <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
                            )}
                        </button>

                        {isAuthenticated && <LogoutButton />}
                    </div>
                </div>

                {showNotifications && (
                    <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200">
                        <div className="p-4">
                            <h3 className="text-lg font-semibold mb-2">Notifications</h3>
                            {notifications.length === 0 ? (
                                <p className="text-gray-500">No new notifications</p>
                            ) : (
                                <ul className="space-y-2">
                                    {notifications.map((notification, index) => (
                                        <li key={index} className="p-2 hover:bg-gray-50 rounded">
                                            {notification.message}
                                        </li>
                                    ))}
                                </ul>
                            )}
                        </div>
                    </div>
                )}
            </header>
        );
    } catch (error) {
        console.error('Header component error:', error);
        reportError(error);
        return <div className="bg-red-50 p-4 text-red-600">Header Error</div>;
    }
}
