import Sidebar from './Sidebar';
import Header from './Header';

export default function MainLayout({ children }) {
    try {
        return (
            <div data-name="main-layout" className="app-container">
                <Sidebar />
                <div data-name="main-content" className="content-wrapper">
                    <Header />
                    <main data-name="page-content" className="py-6">
                        {children}
                    </main>
                </div>
            </div>
        );
    } catch (error) {
        console.error('MainLayout component error:', error);
        console.error(error);
        return (
            <div className="error-fallback">
                <h1>Something went wrong</h1>
                <p>Please refresh the page</p>
            </div>
        );
    }
}
