import { AuthProvider } from '../auth/AuthContext';
import Sidebar from './Sidebar';
import Header from './Header';

export default function MainLayout({ children }) {
    try {
        return (
            <AuthProvider>
                <div data-name="main-layout" className="app-container">
                    <Sidebar />
                    <div data-name="main-content" className="content-wrapper">
                        <Header />
                        <main data-name="page-content" className="py-6">
                            {children}
                        </main>
                    </div>
                </div>
            </AuthProvider>
        );
    } catch (error) {
        console.error('MainLayout component error:', error);
        reportError(error);
        return null;
    }
}
