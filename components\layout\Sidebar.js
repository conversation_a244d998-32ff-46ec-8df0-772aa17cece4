function Sidebar() {
    try {
        const [collapsed, setCollapsed] = React.useState(false);
        const [currentUser, setCurrentUser] = React.useState({
            name: '<PERSON>',
            role: 'Admin'
        });

        const menuItems = [
            { icon: 'fas fa-chart-line', text: 'Dashboard', path: '/' },
            { icon: 'fas fa-users', text: 'Customers', path: '/customers' },
            { icon: 'fas fa-user-plus', text: 'Leads', path: '/leads' },
            { icon: 'fas fa-file-invoice-dollar', text: 'Quotations', path: '/quotations' },
            { icon: 'fas fa-file-invoice', text: 'Invoices', path: '/invoices' },
            { icon: 'fas fa-file-contract', text: 'Contracts', path: '/contracts' },
            { icon: 'fas fa-boxes', text: 'Items', path: '/items' },
            { icon: 'fas fa-chart-bar', text: 'Reports', path: '/reports' },
            { icon: 'fas fa-crown', text: 'Subscription', path: '/subscriptions' },
            { icon: 'fas fa-cog', text: 'Settings', path: '/settings' }
        ];

        const toggleSidebar = () => {
            setCollapsed(!collapsed);
            document.body.classList.toggle('sidebar-collapsed');
        };

        return (
            <div data-name="sidebar" className={`fixed left-0 top-0 h-full bg-gray-800 text-white transition-all duration-300 ${collapsed ? 'w-20' : 'w-64'}`}>
                <div data-name="sidebar-header" className="p-4 border-b border-gray-700">
                    <div data-name="logo-container" className="flex items-center justify-between">
                        {!collapsed && <h1 className="text-xl font-bold">BizManager</h1>}
                        <button 
                            data-name="collapse-button"
                            onClick={toggleSidebar}
                            className="p-2 rounded hover:bg-gray-700"
                        >
                            <i className={`fas fa-${collapsed ? 'arrow-right' : 'arrow-left'}`}></i>
                        </button>
                    </div>
                </div>

                <div data-name="user-profile" className="p-4 border-b border-gray-700">
                    {collapsed ? (
                        <div data-name="user-avatar" className="w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center">
                            <span>{currentUser.name[0]}</span>
                        </div>
                    ) : (
                        <div data-name="user-info" className="flex items-center space-x-3">
                            <div data-name="user-avatar" className="w-10 h-10 rounded-full bg-gray-600 flex items-center justify-center">
                                <span>{currentUser.name[0]}</span>
                            </div>
                            <div>
                                <p className="font-medium">{currentUser.name}</p>
                                <p className="text-sm text-gray-400">{currentUser.role}</p>
                            </div>
                        </div>
                    )}
                </div>

                <nav data-name="sidebar-nav" className="p-4">
                    <ul className="space-y-2">
                        {menuItems.map((item, index) => (
                            <li key={index}>
                                <a
                                    href={item.path}
                                    data-name={`nav-item-${item.text.toLowerCase()}`}
                                    className="flex items-center space-x-3 p-2 rounded hover:bg-gray-700"
                                >
                                    <i className={`${item.icon} w-6`}></i>
                                    {!collapsed && <span>{item.text}</span>}
                                </a>
                            </li>
                        ))}
                    </ul>
                </nav>
            </div>
        );
    } catch (error) {
        console.error('Sidebar component error:', error);
        reportError(error);
        return null;
    }
}
