function LeadActivity({ leadId }) {
    try {
        const [activities, setActivities] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [newActivity, setNewActivity] = React.useState({
            type: 'note',
            description: ''
        });

        React.useEffect(() => {
            fetchActivities();
        }, [leadId]);

        const fetchActivities = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.listObjects(`activity:${leadId}`, 50, true);
                setActivities(response.items);
            } catch (error) {
                console.error('Error fetching activities:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleAddActivity = async (e) => {
            e.preventDefault();
            if (!newActivity.description.trim()) return;

            try {
                await window.apiWrapper.createObject(`activity:${leadId}`, {
                    ...newActivity,
                    createdAt: new Date().toISOString()
                });

                // Clear form and refresh activities
                setNewActivity({ type: 'note', description: '' });
                fetchActivities();
            } catch (error) {
                console.error('Error adding activity:', error);
            }
        };

        const handleInputChange = (e) => {
            const { name, value } = e.target;
            setNewActivity(prev => ({
                ...prev,
                [name]: value
            }));
        };

        return (
            <div data-name="lead-activity" className="space-y-4">
                <form onSubmit={handleAddActivity} className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Activity Type
                        </label>
                        <select
                            name="type"
                            value={newActivity.type}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="note">Note</option>
                            <option value="call">Call</option>
                            <option value="email">Email</option>
                            <option value="meeting">Meeting</option>
                            <option value="task">Task</option>
                            <option value="status_change">Status Change</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Description
                        </label>
                        <textarea
                            name="description"
                            value={newActivity.description}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Enter activity details..."
                        />
                    </div>

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            disabled={!newActivity.description.trim()}
                        >
                            Add Activity
                        </Button>
                    </div>
                </form>

                <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Activity History</h3>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <i className="fas fa-spinner fa-spin text-blue-500"></i>
                        </div>
                    ) : (
                        <div className="lead-activity-timeline">
                            {activities.length === 0 ? (
                                <p className="text-gray-500">No activities recorded</p>
                            ) : (
                                activities.map((activity, index) => (
                                    <ActivityItem key={index} activity={activity} />
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('LeadActivity component error:', error);
        reportError(error);
        return null;
    }
}