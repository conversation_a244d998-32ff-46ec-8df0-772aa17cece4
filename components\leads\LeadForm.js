function LeadForm({ lead, onSubmit, onCancel }) {
    try {
        const [formData, setFormData] = React.useState({
            name: lead?.objectData?.name || '',
            email: lead?.objectData?.email || '',
            phone: lead?.objectData?.phone || '',
            company: lead?.objectData?.company || '',
            source: lead?.objectData?.source || 'website',
            status: lead?.objectData?.status || 'new',
            assignedTo: lead?.objectData?.assignedTo || '',
            value: lead?.objectData?.value || 0,
            notes: lead?.objectData?.notes || '',
            priority: lead?.objectData?.priority || 'medium',
            tags: lead?.objectData?.tags || [],
            followUpDate: lead?.objectData?.followUpDate || ''
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);

        const validateForm = () => {
            const newErrors = {};
            if (!formData.name) {
                newErrors.name = 'Name is required';
            }
            if (formData.email && !isEmailValid(formData.email)) {
                newErrors.email = 'Invalid email format';
            }
            if (formData.phone && !isPhoneValid(formData.phone)) {
                newErrors.phone = 'Invalid phone format';
            }
            if (formData.value < 0) {
                newErrors.value = 'Value cannot be negative';
            }
            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const leadData = {
                    ...formData,
                    updatedAt: new Date().toISOString()
                };

                if (lead?.objectId) {
                    await trickleUpdateObject('lead', lead.objectId, leadData);
                } else {
                    await trickleCreateObject('lead', leadData);
                }

                onSubmit();
            } catch (error) {
                console.error('Error saving lead:', error);
                setErrors({ submit: 'Failed to save lead' });
            } finally {
                setLoading(false);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'number' ? parseFloat(value) || 0 : value
            }));

            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: '' }));
            }
        };

        const handleTagsChange = (tags) => {
            setFormData(prev => ({
                ...prev,
                tags
            }));
        };

        return (
            <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                        label="Name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        error={errors.name}
                        required
                    />

                    <Input
                        label="Email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        error={errors.email}
                    />

                    <Input
                        label="Phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        error={errors.phone}
                    />

                    <Input
                        label="Company"
                        name="company"
                        value={formData.company}
                        onChange={handleInputChange}
                    />

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Source
                        </label>
                        <select
                            name="source"
                            value={formData.source}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="website">Website</option>
                            <option value="referral">Referral</option>
                            <option value="social">Social Media</option>
                            <option value="email">Email Campaign</option>
                            <option value="event">Event</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Status
                        </label>
                        <select
                            name="status"
                            value={formData.status}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="new">New</option>
                            <option value="contacted">Contacted</option>
                            <option value="qualified">Qualified</option>
                            <option value="proposal">Proposal</option>
                            <option value="negotiation">Negotiation</option>
                            <option value="won">Won</option>
                            <option value="lost">Lost</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Priority
                        </label>
                        <select
                            name="priority"
                            value={formData.priority}
                            onChange={handleInputChange}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="low">Low</option>
                            <option value="medium">Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>

                    <Input
                        label="Value"
                        name="value"
                        type="number"
                        value={formData.value}
                        onChange={handleInputChange}
                        error={errors.value}
                    />

                    <Input
                        label="Follow-up Date"
                        name="followUpDate"
                        type="date"
                        value={formData.followUpDate}
                        onChange={handleInputChange}
                    />

                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                            Tags
                        </label>
                        <div className="mt-1">
                            <TagInput
                                tags={formData.tags}
                                onChange={handleTagsChange}
                            />
                        </div>
                    </div>

                    <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <textarea
                            name="notes"
                            value={formData.notes}
                            onChange={handleInputChange}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        />
                    </div>
                </div>

                {errors.submit && (
                    <div className="text-red-600 text-sm">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {lead?.objectId ? 'Update Lead' : 'Create Lead'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('LeadForm component error:', error);
        reportError(error);
        return null;
    }
}
