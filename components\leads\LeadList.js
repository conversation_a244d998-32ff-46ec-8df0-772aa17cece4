function LeadList({ onLeadClick }) {
    try {
        const [leads, setLeads] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [selectedPriority, setSelectedPriority] = React.useState('');
        const [selectedSource, setSelectedSource] = React.useState('');

        React.useEffect(() => {
            fetchLeads();
        }, []);

        const fetchLeads = async () => {
            try {
                setLoading(true);
                const response = await window.fetchLeads();
                setLeads(response);
            } catch (error) {
                console.error('Error fetching leads:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters?.status || '');
            setSelectedPriority(filters?.priority || '');
            setSelectedSource(filters?.source || '');
        };

        const filteredLeads = React.useMemo(() => {
            return leads.filter(lead => {
                const matchesSearch = !searchQuery || 
                    (lead.name && lead.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (lead.email && lead.email.toLowerCase().includes(searchQuery.toLowerCase())) ||
                    (lead.company && lead.company.toLowerCase().includes(searchQuery.toLowerCase()));
                const matchesStatus = !selectedStatus || lead.status === selectedStatus;
                const matchesPriority = !selectedPriority || lead.priority === selectedPriority;
                const matchesSource = !selectedSource || lead.source === selectedSource;
                return matchesSearch && matchesStatus && matchesPriority && matchesSource;
            });
        }, [leads, searchQuery, selectedStatus, selectedPriority, selectedSource]);

        const leadFilters = [
            {
                id: 'status',
                label: 'Status',
                type: 'select',
                options: [
                    { label: 'New', value: 'new' },
                    { label: 'Contacted', value: 'contacted' },
                    { label: 'Qualified', value: 'qualified' },
                    { label: 'Proposal', value: 'proposal' },
                    { label: 'Negotiation', value: 'negotiation' },
                    { label: 'Won', value: 'won' },
                    { label: 'Lost', value: 'lost' }
                ]
            },
            {
                id: 'priority',
                label: 'Priority',
                type: 'select',
                options: [
                    { label: 'High', value: 'high' },
                    { label: 'Medium', value: 'medium' },
                    { label: 'Low', value: 'low' }
                ]
            },
            {
                id: 'source',
                label: 'Source',
                type: 'select',
                options: [
                    { label: 'Website', value: 'website' },
                    { label: 'Referral', value: 'referral' },
                    { label: 'Social Media', value: 'social' },
                    { label: 'Email Campaign', value: 'email' },
                    { label: 'Event', value: 'event' },
                    { label: 'Other', value: 'other' }
                ]
            }
        ];

        const columns = [
            { key: 'name', label: 'Name' },
            { 
                key: 'company', 
                label: 'Company',
                render: (row) => row.company || '-'
            },
            // { key: 'value', label: 'Value', render: (row) => formatCurrency(row.value) }, // Uncomment if value exists in schema
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.status === 'won' ? 'bg-green-100 text-green-800' :
                        row.status === 'lost' ? 'bg-red-100 text-red-800' :
                        row.status === 'qualified' ? 'bg-blue-100 text-blue-800' :
                        row.status === 'proposal' ? 'bg-purple-100 text-purple-800' :
                        row.status === 'negotiation' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                    }`}>
                        {row.status ? row.status.charAt(0).toUpperCase() + row.status.slice(1) : ''}
                    </span>
                )
            },
            {
                key: 'priority',
                label: 'Priority',
                render: (row) => (
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        row.priority === 'high' ? 'bg-red-100 text-red-800' :
                        row.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                    }`}>
                        {row.priority ? row.priority.charAt(0).toUpperCase() + row.priority.slice(1) : ''}
                    </span>
                )
            },
            // { key: 'followUpDate', label: 'Follow-up Date', render: (row) => row.followUpDate ? formatDate(row.followUpDate) : '-' }, // Uncomment if followUpDate exists
            {
                key: 'source',
                label: 'Source',
                render: (row) => (
                    <span className="inline-flex items-center">
                        <i className={`mr-1 fas ${
                            row.source === 'website' ? 'fa-globe' :
                            row.source === 'referral' ? 'fa-user-friends' :
                            row.source === 'social' ? 'fa-share-alt' :
                            row.source === 'email' ? 'fa-envelope' :
                            row.source === 'event' ? 'fa-calendar' :
                            'fa-question-circle'
                        }`}></i>
                        {row.source ? row.source.charAt(0).toUpperCase() + row.source.slice(1) : ''}
                    </span>
                )
            }
        ];

        return (
            <div data-name="lead-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search leads..."
                        filters={leadFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredLeads}
                    loading={loading}
                    onRowClick={(lead) => onLeadClick({ objectData: lead })}
                    emptyMessage="No leads found"
                />
            </div>
        );
    } catch (error) {
        console.error('LeadList component error:', error);
        reportError(error);
        return null;
    }
}