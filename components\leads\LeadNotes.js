function LeadNotes({ leadId }) {
    try {
        const [notes, setNotes] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [newNote, setNewNote] = React.useState('');

        React.useEffect(() => {
            fetchNotes();
        }, [leadId]);

        const fetchNotes = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.listObjects(`note:${leadId}`, 50, true);
                setNotes(response.items);
            } catch (error) {
                console.error('Error fetching notes:', error);
            } finally {
                setLoading(false);
            }
        };

        const handleAddNote = async (e) => {
            e.preventDefault();
            if (!newNote.trim()) return;

            try {
                await window.apiWrapper.createObject(`note:${leadId}`, {
                    content: newNote,
                    createdAt: new Date().toISOString()
                });

                // Also create an activity for the note
                await window.apiWrapper.createObject(`activity:${leadId}`, {
                    type: 'note',
                    description: 'Added a new note',
                    createdAt: new Date().toISOString()
                });

                setNewNote('');
                fetchNotes();
            } catch (error) {
                console.error('Error adding note:', error);
            }
        };

        return (
            <div data-name="lead-notes" className="space-y-4">
                <form onSubmit={handleAddNote}>
                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Add Note
                        </label>
                        <textarea
                            value={newNote}
                            onChange={(e) => setNewNote(e.target.value)}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Enter your note..."
                        />
                    </div>
                    <div className="mt-2 flex justify-end">
                        <Button
                            type="submit"
                            disabled={!newNote.trim()}
                        >
                            Add Note
                        </Button>
                    </div>
                </form>

                <div className="mt-6">
                    <h3 className="text-lg font-medium mb-4">Notes History</h3>
                    {loading ? (
                        <div className="flex justify-center items-center h-32">
                            <i className="fas fa-spinner fa-spin text-blue-500"></i>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {notes.length === 0 ? (
                                <p className="text-gray-500">No notes yet</p>
                            ) : (
                                notes.map((note, index) => (
                                    <div
                                        key={index}
                                        className="bg-gray-50 p-4 rounded-lg"
                                    >
                                        <p className="text-gray-900 whitespace-pre-line">
                                            {note.objectData.content}
                                        </p>
                                        <p className="text-xs text-gray-500 mt-2">
                                            {formatDateTime(note.createdAt)}
                                        </p>
                                    </div>
                                ))
                            )}
                        </div>
                    )}
                </div>
            </div>
        );
    } catch (error) {
        console.error('LeadNotes component error:', error);
        reportError(error);
        return null;
    }
}