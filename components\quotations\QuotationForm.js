function QuotationForm({ quotation, onSubmit, onCancel }) {
    try {
        // Define the function before using it in useState
        const generateQuotationNumber = () => {
            const prefix = 'QT';
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
            return `${prefix}-${timestamp}-${random}`;
        };

        const [formData, setFormData] = React.useState({
            customer: quotation?.objectData?.customer || '',
            items: quotation?.objectData?.items || [{ description: '', quantity: 1, price: 0, itemId: '' }],
            notes: quotation?.objectData?.notes || '',
            terms: quotation?.objectData?.terms || '',
            validUntil: quotation?.objectData?.validUntil || '',
            status: quotation?.objectData?.status || 'draft',
            quotationNumber: quotation?.objectData?.quotationNumber || generateQuotationNumber(),
            projectName: quotation?.objectData?.projectName || '',
            taxRate: quotation?.objectData?.taxRate || 18, // Default GST rate
            discount: quotation?.objectData?.discount || 0
        });

        const [errors, setErrors] = React.useState({});
        const [loading, setLoading] = React.useState(false);
        const [customers, setCustomers] = React.useState([]);
        const [itemsList, setItemsList] = React.useState([]);
        const [selectedCustomerData, setSelectedCustomerData] = React.useState(null);
        const [settings, setSettings] = React.useState({ taxRate: 18 }); // Default tax rate if settings not found

        React.useEffect(() => {
            fetchCustomers();
            fetchItems();
            fetchSettings();
            
            // Set default validUntil date if not provided (30 days from now)
            if (!formData.validUntil) {
                const thirtyDaysFromNow = new Date();
                thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
                setFormData(prev => ({
                    ...prev,
                    validUntil: thirtyDaysFromNow.toISOString().split('T')[0]
                }));
            }
        }, []);

        React.useEffect(() => {
            if (formData.customer) {
                fetchCustomerDetails(formData.customer);
            } else {
                setSelectedCustomerData(null);
            }
        }, [formData.customer]);

        const fetchCustomers = async () => {
            try {
                const response = await window.apiWrapper.listObjects('customer', 100, true);
                setCustomers(response.items);
            } catch (error) {
                console.error('Error fetching customers:', error);
            }
        };

        const fetchItems = async () => {
            try {
                const response = await window.apiWrapper.listObjects('item', 100, true);
                // Only show active items
                const activeItems = response.items.filter(item => item.objectData.isActive !== false);
                setItemsList(activeItems);
            } catch (error) {
                console.error('Error fetching items:', error);
            }
        };

        const fetchSettings = async () => {
            try {
                const response = await window.apiWrapper.listObjects('settings', 1, true);
                if (response.items.length > 0) {
                    setSettings(response.items[0].objectData);
                    
                    // Update tax rate from settings if this is a new quotation
                    if (!quotation?.objectId) {
                        setFormData(prev => ({
                            ...prev,
                            taxRate: response.items[0].objectData.taxRate || 18
                        }));
                    }
                }
            } catch (error) {
                console.error('Error fetching settings:', error);
            }
        };

        const fetchCustomerDetails = async (customerId) => {
            try {
                const response = await window.apiWrapper.getObject('customer', customerId);
                setSelectedCustomerData(response.objectData);
            } catch (error) {
                console.error('Error fetching customer details:', error);
            }
        };

        const handleAddItem = () => {
            setFormData(prev => ({
                ...prev,
                items: [...prev.items, { description: '', quantity: 1, price: 0, itemId: '' }]
            }));
        };

        const handleRemoveItem = (index) => {
            setFormData(prev => ({
                ...prev,
                items: prev.items.filter((_, i) => i !== index)
            }));
        };

        const handleItemChange = (index, field, value) => {
            setFormData(prev => ({
                ...prev,
                items: prev.items.map((item, i) => 
                    i === index ? { ...item, [field]: value } : item
                )
            }));
        };

        const handleItemSelection = (index, itemId) => {
            if (!itemId) {
                handleItemChange(index, 'itemId', '');
                handleItemChange(index, 'description', '');
                handleItemChange(index, 'price', 0);
                return;
            }

            const selectedItem = itemsList.find(item => item.objectId === itemId);
            if (selectedItem) {
                handleItemChange(index, 'itemId', itemId);
                handleItemChange(index, 'description', selectedItem.objectData.name);
                handleItemChange(index, 'price', selectedItem.objectData.price);
            }
        };

        const calculateSubtotal = () => {
            return formData.items.reduce((total, item) => 
                total + (item.quantity * item.price), 0
            );
        };

        const calculateTax = (subtotal) => {
            const taxRate = formData.taxRate || 18; // Use form tax rate
            const discountedSubtotal = subtotal - formData.discount;
            return (discountedSubtotal > 0 ? discountedSubtotal : 0) * (taxRate / 100);
        };

        const calculateTotal = () => {
            const subtotal = calculateSubtotal();
            const discountedSubtotal = subtotal - formData.discount;
            const tax = calculateTax(subtotal);
            return discountedSubtotal + tax;
        };

        const validateForm = () => {
            const newErrors = {};
            if (!formData.customer) {
                newErrors.customer = 'Customer is required';
            }
            if (formData.items.length === 0) {
                newErrors.items = 'At least one item is required';
            }
            if (!formData.validUntil) {
                newErrors.validUntil = 'Valid until date is required';
            }
            if (formData.discount < 0) {
                newErrors.discount = 'Discount cannot be negative';
            }
            if (formData.taxRate < 0 || formData.taxRate > 100) {
                newErrors.taxRate = 'Tax rate must be between 0 and 100';
            }

            // Validate each item has a description and price
            formData.items.forEach((item, index) => {
                if (!item.description) {
                    newErrors[`item_${index}_description`] = 'Description is required';
                }
                if (item.price <= 0) {
                    newErrors[`item_${index}_price`] = 'Price must be greater than zero';
                }
            });

            setErrors(newErrors);
            return Object.keys(newErrors).length === 0;
        };

        const handleSubmit = async (e) => {
            e.preventDefault();
            if (!validateForm()) return;

            try {
                setLoading(true);
                const subtotal = calculateSubtotal();
                const tax = calculateTax(subtotal);
                const total = calculateTotal();
                
                const quotationData = {
                    ...formData,
                    subtotal,
                    tax,
                    total,
                    updatedAt: new Date().toISOString()
                };

                if (quotation?.objectId) {
                    await trickleUpdateObject('quotation', quotation.objectId, quotationData);
                } else {
                    await trickleCreateObject('quotation', quotationData);
                }

                onSubmit();
            } catch (error) {
                console.error('Error saving quotation:', error);
                setErrors({ submit: 'Failed to save quotation' });
            } finally {
                setLoading(false);
            }
        };

        const handleInputChange = (e) => {
            const { name, value, type } = e.target;
            setFormData(prev => ({
                ...prev,
                [name]: type === 'number' ? parseFloat(value) || 0 : value
            }));
        };

        return (
            <form data-name="quotation-form" onSubmit={handleSubmit} className="quotation-form">
                <div className="grid grid-cols-1 gap-6">
                    <div className="flex justify-between items-center">
                        <div className="text-xl font-bold text-gray-700">
                            Quotation #{formData.quotationNumber}
                        </div>
                        <div>
                            <Button
                                type="button"
                                variant="secondary"
                                icon="fas fa-random"
                                onClick={() => setFormData(prev => ({
                                    ...prev,
                                    quotationNumber: generateQuotationNumber()
                                }))}
                                className="text-sm"
                            >
                                Regenerate Number
                            </Button>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Customer <span className="text-red-500">*</span>
                            </label>
                            <select
                                name="customer"
                                value={formData.customer}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="">Select Customer</option>
                                {customers.map(customer => (
                                    <option key={customer.objectId} value={customer.objectId}>
                                        {customer.objectData.name}
                                    </option>
                                ))}
                            </select>
                            {errors.customer && (
                                <p className="mt-1 text-sm text-red-600">{errors.customer}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Project Name
                            </label>
                            <input
                                type="text"
                                name="projectName"
                                value={formData.projectName}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Optional project name or reference"
                            />
                        </div>
                    </div>

                    {selectedCustomerData && (
                        <div className="bg-gray-50 p-4 rounded-md">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">Customer Information</h3>
                            <div className="text-sm">
                                <p><span className="font-medium">Name:</span> {selectedCustomerData.name}</p>
                                {selectedCustomerData.company && <p><span className="font-medium">Company:</span> {selectedCustomerData.company}</p>}
                                {selectedCustomerData.email && <p><span className="font-medium">Email:</span> {selectedCustomerData.email}</p>}
                                {selectedCustomerData.phone && <p><span className="font-medium">Phone:</span> {selectedCustomerData.phone}</p>}
                            </div>
                        </div>
                    )}

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Items <span className="text-red-500">*</span>
                        </label>
                        <div className="quotation-items">
                            {formData.items.map((item, index) => (
                                <div key={index} className="quotation-item">
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Item</label>
                                        <select
                                            value={item.itemId || ''}
                                            onChange={(e) => handleItemSelection(index, e.target.value)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        >
                                            <option value="">Select Item</option>
                                            {itemsList.map(catalogItem => (
                                                <option key={catalogItem.objectId} value={catalogItem.objectId}>
                                                    {catalogItem.objectData.name} - {formatCurrency(catalogItem.objectData.price)}
                                                </option>
                                            ))}
                                        </select>
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Description</label>
                                        <input
                                            type="text"
                                            placeholder="Description"
                                            value={item.description}
                                            onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                        {errors[`item_${index}_description`] && (
                                            <p className="mt-1 text-xs text-red-600">{errors[`item_${index}_description`]}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Quantity</label>
                                        <input
                                            type="number"
                                            placeholder="Quantity"
                                            value={item.quantity}
                                            onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 0)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Price</label>
                                        <input
                                            type="number"
                                            placeholder="Price"
                                            value={item.price}
                                            onChange={(e) => handleItemChange(index, 'price', parseFloat(e.target.value) || 0)}
                                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                        />
                                        {errors[`item_${index}_price`] && (
                                            <p className="mt-1 text-xs text-red-600">{errors[`item_${index}_price`]}</p>
                                        )}
                                    </div>
                                    <div>
                                        <label className="block text-xs text-gray-500 mb-1">Total</label>
                                        <div className="text-right py-2 px-2 bg-gray-50 rounded-md">
                                            {formatCurrency(item.quantity * item.price)}
                                        </div>
                                    </div>
                                    <div className="flex items-end">
                                        <button
                                            type="button"
                                            onClick={() => handleRemoveItem(index)}
                                            className="text-red-600 hover:text-red-800"
                                        >
                                            <i className="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                        <button
                            type="button"
                            onClick={handleAddItem}
                            className="mt-2 text-blue-600 hover:text-blue-800"
                        >
                            <i className="fas fa-plus mr-1"></i>
                            Add Item
                        </button>
                        {errors.items && (
                            <p className="mt-1 text-sm text-red-600">{errors.items}</p>
                        )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Discount</label>
                            <input
                                type="number"
                                name="discount"
                                value={formData.discount}
                                onChange={handleInputChange}
                                min="0"
                                step="1"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.discount && (
                                <p className="mt-1 text-sm text-red-600">{errors.discount}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Tax Rate (%)</label>
                            <input
                                type="number"
                                name="taxRate"
                                value={formData.taxRate}
                                onChange={handleInputChange}
                                min="0"
                                max="100"
                                step="0.1"
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.taxRate && (
                                <p className="mt-1 text-sm text-red-600">{errors.taxRate}</p>
                            )}
                        </div>
                    </div>

                    <div className="invoice-totals mt-6 bg-gray-50 p-4 rounded-lg">
                        <div className="grid grid-cols-2 gap-2">
                            <div className="text-right text-gray-600">Subtotal:</div>
                            <div className="text-right font-medium">{formatCurrency(calculateSubtotal())}</div>

                            <div className="text-right text-gray-600">Discount:</div>
                            <div className="text-right font-medium">- {formatCurrency(formData.discount)}</div>

                            <div className="text-right text-gray-600">Tax ({formData.taxRate}%):</div>
                            <div className="text-right font-medium">{formatCurrency(calculateTax(calculateSubtotal()))}</div>

                            <div className="text-right font-bold text-lg border-t pt-2 mt-2">Total:</div>
                            <div className="text-right text-xl font-bold border-t pt-2 mt-2">{formatCurrency(calculateTotal())}</div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Valid Until <span className="text-red-500">*</span>
                            </label>
                            <input
                                type="date"
                                name="validUntil"
                                value={formData.validUntil}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                            {errors.validUntil && (
                                <p className="mt-1 text-sm text-red-600">{errors.validUntil}</p>
                            )}
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700">
                                Status
                            </label>
                            <select
                                name="status"
                                value={formData.status}
                                onChange={handleInputChange}
                                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="draft">Draft</option>
                                <option value="sent">Sent</option>
                                <option value="accepted">Accepted</option>
                                <option value="rejected">Rejected</option>
                                <option value="expired">Expired</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <textarea
                            name="notes"
                            value={formData.notes}
                            onChange={handleInputChange}
                            rows={3}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Additional notes for the customer"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700">
                            Terms and Conditions
                        </label>
                        <textarea
                            name="terms"
                            value={formData.terms}
                            onChange={handleInputChange}
                            rows={4}
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            placeholder="Terms and conditions for this quotation"
                        />
                    </div>
                </div>

                {errors.submit && (
                    <div className="mt-4 text-red-600 text-sm">
                        {errors.submit}
                    </div>
                )}

                <div className="flex justify-end space-x-4 mt-6">
                    <Button
                        type="button"
                        variant="secondary"
                        onClick={onCancel}
                    >
                        Cancel
                    </Button>
                    <Button
                        type="submit"
                        loading={loading}
                        disabled={loading}
                    >
                        {quotation?.objectId ? 'Update Quotation' : 'Create Quotation'}
                    </Button>
                </div>
            </form>
        );
    } catch (error) {
        console.error('QuotationForm component error:', error);
        reportError(error);
        return null;
    }
}