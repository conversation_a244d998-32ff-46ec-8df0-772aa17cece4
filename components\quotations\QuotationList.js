function QuotationList({ onQuotationClick, onEdit, onDelete, onPrint }) {
    try {
        const [quotations, setQuotations] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [searchQuery, setSearchQuery] = React.useState('');
        const [selectedStatus, setSelectedStatus] = React.useState('');
        const [customersMap, setCustomersMap] = React.useState({});

        React.useEffect(() => {
            fetchQuotations();
            fetchCustomers();
        }, []);

        const fetchQuotations = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.getQuotations();
                setQuotations(response);
            } catch (error) {
                console.error('Error fetching quotations:', error);
            } finally {
                setLoading(false);
            }
        };
        
        const fetchCustomers = async () => {
            try {
                const response = await window.fetchCustomers();
                const customerMap = {};
                response.forEach(customer => {
                    customerMap[customer.id] = customer;
                });
                setCustomersMap(customerMap);
            } catch (error) {
                console.error('Error fetching customers:', error);
            }
        };

        const handleSearch = (query, filters) => {
            setSearchQuery(query);
            setSelectedStatus(filters?.status || '');
            
            if (filters?.dateRange?.startDate && filters?.dateRange?.endDate) {
                setDateRange({
                    startDate: filters.dateRange.startDate,
                    endDate: filters.dateRange.endDate
                });
            } else {
                setDateRange(null);
            }
        };

        const [dateRange, setDateRange] = React.useState(null);

        const filteredQuotations = React.useMemo(() => {
            return quotations.filter(quotation => {
                const customerName = customersMap[quotation.objectData.customer]?.name || '';
                const quotationNumber = quotation.objectData.quotationNumber || '';
                const createdAt = new Date(quotation.createdAt);
                
                const matchesSearch = !searchQuery || 
                    customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                    quotationNumber.toLowerCase().includes(searchQuery.toLowerCase());
                
                const matchesStatus = !selectedStatus || quotation.objectData.status === selectedStatus;

                const matchesDateRange = !dateRange || (
                    createdAt >= new Date(dateRange.startDate) && 
                    createdAt <= new Date(dateRange.endDate)
                );

                return matchesSearch && matchesStatus && matchesDateRange;
            });
        }, [quotations, searchQuery, selectedStatus, customersMap, dateRange]);

        const quotationStatusFilters = [
            { id: 'status', label: 'Status', type: 'select', options: [
                { label: 'All Statuses', value: '' },
                { label: 'Draft', value: 'draft' },
                { label: 'Sent', value: 'sent' },
                { label: 'Accepted', value: 'accepted' },
                { label: 'Rejected', value: 'rejected' }
            ]},
            { 
                id: 'dateRange', 
                label: 'Date Range', 
                type: 'dateRange',
                defaultStartDate: new Date(new Date().setMonth(new Date().getMonth() - 1)),
                defaultEndDate: new Date()
            }
        ];

        const handleEdit = (quotation, e) => {
            e.stopPropagation();
            if (onEdit) onEdit(quotation);
        };

        const handleDelete = (quotation, e) => {
            e.stopPropagation();
            if (onDelete) onDelete(quotation);
        };

        

        const columns = [
            { 
                key: 'quotationNumber', 
                label: 'Quotation #',
                render: (row) => row.objectData.quotationNumber || `#${row.objectId.substring(0, 8)}`,
                sortable: true,
                sortFunction: (a, b) => (a.objectData.quotationNumber || `#${a.objectId}`).localeCompare(
                    b.objectData.quotationNumber || `#${b.objectId}`
                )
            },
            { 
                key: 'customer', 
                label: 'Customer',
                render: (row) => customersMap[row.objectData.customer]?.name || 'Unknown Customer',
                sortable: true,
                sortFunction: (a, b) => (
                    customersMap[a.objectData.customer]?.name || ''
                ).localeCompare(
                    customersMap[b.objectData.customer]?.name || ''
                )
            },
            { 
                key: 'total', 
                label: 'Total',
                render: (row) => formatCurrency(row.objectData.total),
                sortable: true,
                sortFunction: (a, b) => (a.objectData.total || 0) - (b.objectData.total || 0)
            },
            {
                key: 'validUntil',
                label: 'Valid Until',
                render: (row) => formatDate(row.objectData.validUntil),
                sortable: true,
                sortFunction: (a, b) => new Date(a.objectData.validUntil||0) - new Date(b.objectData.validUntil||0)
            },
            {
                key: 'status',
                label: 'Status',
                render: (row) => (
                    <span className={`quotation-status ${row.objectData.status}`}>
                        {row.objectData.status.charAt(0).toUpperCase() + row.objectData.status.slice(1)}
                    </span>
                ),
                sortable: true,
                sortFunction: (a, b) => a.objectData.status.localeCompare(b.objectData.status)
            },
            {
                key: 'actions',
                label: 'Actions',
                render: (row) => (
                    <div className="flex space-x-2">
                        <button
                            onClick={(e) => handleEdit(row, e)}
                            className="text-blue-600 hover:text-blue-800"
                            title="Edit Quotation"
                        >
                            <i className="fas fa-edit"></i>
                        </button>
                        <button
                            onClick={(e) => { e.stopPropagation(); onPrint(row); }}
                            className="text-green-600 hover:text-green-800"
                            title="Print Quotation"
                        >
                            <i className="fas fa-print"></i>
                        </button>
                        <button
                            onClick={(e) => { e.stopPropagation(); /* TODO: Implement email sending */ }}
                            className="text-purple-600 hover:text-purple-800"
                            title="Send Email"
                        >
                            <i className="fas fa-envelope"></i>
                        </button>
                        <button
                            onClick={(e) => { e.stopPropagation(); /* TODO: Implement duplication */ }}
                            className="text-orange-600 hover:text-orange-800"
                            title="Duplicate Quotation"
                        >
                            <i className="fas fa-copy"></i>
                        </button>
                        <button
                            onClick={(e) => { e.stopPropagation(); /* TODO: Implement preview */ }}
                            className="text-teal-600 hover:text-teal-800"
                            title="Preview Quotation"
                        >
                            <i className="fas fa-eye"></i>
                        </button>
                        <button
                            onClick={(e) => handleDelete(row, e)}
                            className="text-red-600 hover:text-red-800"
                            title="Delete Quotation"
                        >
                            <i className="fas fa-trash"></i>
                        </button>
                    </div>
                )
            }
        ];

        return (
            <div data-name="quotation-list">
                <div className="mb-6">
                    <SearchBar
                        value={searchQuery}
                        onChange={setSearchQuery}
                        onSearch={handleSearch}
                        placeholder="Search quotations..."
                        filters={quotationStatusFilters}
                    />
                </div>

                <Table
                    columns={columns}
                    data={filteredQuotations}
                    loading={loading}
                    onRowClick={onQuotationClick}
                    emptyMessage="No quotations found"
                />
            </div>
        );
    } catch (error) {
        console.error('QuotationList component error:', error);
        reportError(error);
        return null;
    }
}
