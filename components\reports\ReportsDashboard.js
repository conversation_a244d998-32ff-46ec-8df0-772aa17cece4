function ReportsDashboard({ reportData, showNotification }) {
    try {
        const [loading, setLoading] = React.useState(!reportData);
        const [dateRange, setDateRange] = React.useState('month');

        // Use provided reportData or initialize with defaults
        const data = reportData || {
            salesSummary: { total_invoices: 0, total_revenue: 0, paid_revenue: 0, overdue_amount: 0 },
            monthlyRevenue: [],
            topCustomers: []
        };

        if (loading && !reportData) {
            return (
                <div className="flex justify-center items-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>
            );
        }

        return (
            <div data-name="reports-dashboard" className="space-y-6">
                <div className="flex justify-between items-center">
                    <h2 className="text-xl font-bold">Business Reports</h2>
                    <select 
                        value={dateRange} 
                        onChange={(e) => setDateRange(e.target.value)}
                        className="border rounded px-3 py-2"
                    >
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                        <option value="quarter">This Quarter</option>
                        <option value="year">This Year</option>
                    </select>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Revenue</p>
                                <p className="text-2xl font-bold">${formatCurrency(data.salesSummary.total_revenue)}</p>
                            </div>
                            <div className="p-3 bg-green-100 rounded-full">
                                <i className="fas fa-dollar-sign text-green-600"></i>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Total Invoices</p>
                                <p className="text-2xl font-bold">{data.salesSummary.total_invoices}</p>
                            </div>
                            <div className="p-3 bg-blue-100 rounded-full">
                                <i className="fas fa-file-invoice text-blue-600"></i>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Paid Revenue</p>
                                <p className="text-2xl font-bold">${formatCurrency(data.salesSummary.paid_revenue)}</p>
                            </div>
                            <div className="p-3 bg-purple-100 rounded-full">
                                <i className="fas fa-check-circle text-purple-600"></i>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Overdue Amount</p>
                                <p className="text-2xl font-bold">${formatCurrency(data.salesSummary.overdue_amount)}</p>
                            </div>
                            <div className="p-3 bg-red-100 rounded-full">
                                <i className="fas fa-exclamation-triangle text-red-600"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <h3 className="text-lg font-semibold mb-4">Revenue Overview</h3>
                        <div className="h-64 bg-gray-50 rounded p-4">
                            {data.monthlyRevenue && data.monthlyRevenue.length > 0 ? (
                                <div className="flex justify-around items-end h-full">
                                    {data.monthlyRevenue.map((item, index) => {
                                        const maxRevenue = Math.max(...data.monthlyRevenue.map(i => i.revenue));
                                        const barHeight = maxRevenue > 0 ? (item.revenue / maxRevenue) * 100 : 0;
                                        return (
                                            <div key={index} className="flex flex-col items-center">
                                                <div className="text-xs text-gray-500">${formatCurrency(item.revenue)}</div>
                                                <div className="w-8 bg-blue-500 rounded-t" style={{ height: `${barHeight}%` }}></div>
                                                <div className="text-sm font-medium mt-1">{new Date(item.month + '-02').toLocaleString('default', { month: 'short' })}</div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="flex items-center justify-center h-full">
                                    <p className="text-gray-500">No revenue data available for the selected period.</p>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <h3 className="text-lg font-semibold mb-4">Top Customers</h3>
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoices</th>
                                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Spent</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {data.topCustomers && data.topCustomers.map((customer, index) => (
                                        <tr key={index}>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{customer.name}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{customer.invoice_count}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${formatCurrency(customer.total_spent)}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ReportsDashboard component error:', error);
        return <div className="text-red-600">Error loading reports dashboard</div>;
    }
}