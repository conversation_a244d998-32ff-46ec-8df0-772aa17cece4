function CompanySettings({ formData, handleInputChange }) {
    try {
        return (
            <div className="space-y-8">
                <h2 className="text-xl font-bold text-gray-800 mb-6">Company Information</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Input
                        label="Company Name"
                        name="companyName"
                        value={formData.companyName || ''}
                        onChange={handleInputChange}
                        required
                    />
                    <Input
                        label="Company Email"
                        name="companyEmail"
                        type="email"
                        value={formData.companyEmail || ''}
                        onChange={handleInputChange}
                        required
                    />
                    <Input
                        label="Company Phone"
                        name="companyPhone"
                        value={formData.companyPhone || ''}
                        onChange={handleInputChange}
                    />
                    <Input
                        label="Company Website"
                        name="companyWebsite"
                        value={formData.companyWebsite || ''}
                        onChange={handleInputChange}
                    />
                    <Input
                        label="Company Logo URL"
                        name="companyLogo"
                        value={formData.companyLogo || ''}
                        onChange={handleInputChange}
                        placeholder="https://example.com/logo.png"
                    />
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Currency
                        </label>
                        <select
                            name="companyCurrency"
                            value={formData.companyCurrency || 'USD'}
                            onChange={handleInputChange}
                            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        >
                            <option value="USD">USD ($)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="CAD">CAD ($)</option>
                            <option value="AUD">AUD ($)</option>
                            <option value="INR">INR (₹)</option>
                        </select>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Address Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Company Address
                            </label>
                            <textarea
                                name="companyAddress"
                                value={formData.companyAddress || ''}
                                onChange={handleInputChange}
                                rows={3}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            />
                        </div>
                        <Input
                            label="City"
                            name="companyCity"
                            value={formData.companyCity || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="State/Province"
                            name="companyState"
                            value={formData.companyState || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="ZIP/Postal Code"
                            name="companyZip"
                            value={formData.companyZip || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="Country"
                            name="companyCountry"
                            value={formData.companyCountry || ''}
                            onChange={handleInputChange}
                        />
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Tax & Legal Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                            label="GSTIN"
                            name="companyGST"
                            value={formData.companyGST || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="Registration Number"
                            name="companyRegistration"
                            value={formData.companyRegistration || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="Default Tax Rate (%)"
                            name="companyTaxRate"
                            type="number"
                            value={formData.companyTaxRate || '0'}
                            onChange={handleInputChange}
                            placeholder="0"
                        />
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Payment Terms
                            </label>
                            <select
                                name="companyPaymentTerms"
                                value={formData.companyPaymentTerms || 'Net 30'}
                                onChange={handleInputChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="Due on Receipt">Due on Receipt</option>
                                <option value="Net 7">Net 7 days</option>
                                <option value="Net 15">Net 15 days</option>
                                <option value="Net 30">Net 30 days</option>
                                <option value="Net 60">Net 60 days</option>
                                <option value="Net 90">Net 90 days</option>
                            </select>
                        </div>
                        <Input
                            label="Authorized Signatory Name"
                            name="authorizedName"
                            value={formData.authorizedName || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="UPI ID (for QR payments)"
                            name="upiId"
                            value={formData.upiId || ''}
                            onChange={handleInputChange}
                            placeholder="yourname@upi"
                        />
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Banking Details</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                            label="Bank Name"
                            name="companyBankName"
                            value={formData.companyBankName || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="Account Number"
                            name="companyBankAccount"
                            value={formData.companyBankAccount || ''}
                            onChange={handleInputChange}
                        />
                        <Input
                            label="IFSC/Routing Number"
                            name="companyBankIFSC"
                            value={formData.companyBankIFSC || ''}
                            onChange={handleInputChange}
                        />
                        <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Payment Instructions / Bank Details
                            </label>
                            <textarea
                                name="bankDetails"
                                value={formData.bankDetails || ''}
                                onChange={handleInputChange}
                                rows={4}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Account Name: Your Company Name&#10;Account Number: **********&#10;IFSC Code: ABCD0123456&#10;Bank Name: Your Bank&#10;Branch: Your Branch"
                            />
                            <p className="mt-1 text-xs text-gray-500">
                                These details will appear on invoices for payments.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('CompanySettings component error:', error);
        return null;
    }
}