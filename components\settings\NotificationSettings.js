function NotificationSettings({ formData, handleNotificationChange }) {
    try {
        // Ensure notifications object exists with default values
        const notifications = formData.notifications || {
            email: true,
            browser: true,
            invoice: true,
            quotation: true,
            contract: true
        };

        return (
            <div className="space-y-6">
                <h2 className="text-lg font-medium">Notification Preferences</h2>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-sm font-medium text-gray-700">Email Notifications</h3>
                            <p className="text-sm text-gray-500">Receive notifications via email</p>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="email-notifications"
                                checked={notifications.email !== false}
                                onChange={() => handleNotificationChange('email')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="email-notifications" className="ml-2 block text-sm text-gray-900">
                                {notifications.email !== false ? 'Enabled' : 'Disabled'}
                            </label>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <h3 className="text-sm font-medium text-gray-700">Browser Notifications</h3>
                            <p className="text-sm text-gray-500">Show notifications in browser</p>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="browser-notifications"
                                checked={notifications.browser !== false}
                                onChange={() => handleNotificationChange('browser')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="browser-notifications" className="ml-2 block text-sm text-gray-900">
                                {notifications.browser !== false ? 'Enabled' : 'Disabled'}
                            </label>
                        </div>
                    </div>

                    <hr className="my-4" />

                    <h3 className="text-sm font-medium text-gray-700">Notification Events</h3>

                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-500">Invoice notifications</p>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="invoice-notifications"
                                checked={notifications.invoice !== false}
                                onChange={() => handleNotificationChange('invoice')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="invoice-notifications" className="ml-2 block text-sm text-gray-900">
                                {notifications.invoice !== false ? 'Enabled' : 'Disabled'}
                            </label>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-500">Quotation notifications</p>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="quotation-notifications"
                                checked={notifications.quotation !== false}
                                onChange={() => handleNotificationChange('quotation')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="quotation-notifications" className="ml-2 block text-sm text-gray-900">
                                {notifications.quotation !== false ? 'Enabled' : 'Disabled'}
                            </label>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-sm text-gray-500">Contract notifications</p>
                        </div>
                        <div className="flex items-center">
                            <input
                                type="checkbox"
                                id="contract-notifications"
                                checked={notifications.contract !== false}
                                onChange={() => handleNotificationChange('contract')}
                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                            />
                            <label htmlFor="contract-notifications" className="ml-2 block text-sm text-gray-900">
                                {notifications.contract !== false ? 'Enabled' : 'Disabled'}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('NotificationSettings component error:', error);
        reportError(error);
        return null;
    }
}
