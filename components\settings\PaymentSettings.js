function PaymentSettings({ formData, handleInputChange }) {
    try {
        return (
            <div className="space-y-8">
                <h2 className="text-xl font-bold text-gray-800 mb-6">Payment Settings</h2>
                
                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Methods</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="acceptBankTransfer"
                                    id="acceptBankTransfer"
                                    checked={formData.acceptBankTransfer !== false}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="acceptBankTransfer" className="ml-2 block text-sm text-gray-900">
                                    Bank Transfer
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="acceptCreditCard"
                                    id="acceptCreditCard"
                                    checked={formData.acceptCreditCard || false}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="acceptCreditCard" className="ml-2 block text-sm text-gray-900">
                                    Credit/Debit Card
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="acceptUPI"
                                    id="acceptUPI"
                                    checked={formData.acceptUPI || false}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="acceptUPI" className="ml-2 block text-sm text-gray-900">
                                    UPI Payments
                                </label>
                            </div>
                        </div>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="acceptCash"
                                    id="acceptCash"
                                    checked={formData.acceptCash || false}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="acceptCash" className="ml-2 block text-sm text-gray-900">
                                    Cash Payment
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="acceptCheque"
                                    id="acceptCheque"
                                    checked={formData.acceptCheque || false}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="acceptCheque" className="ml-2 block text-sm text-gray-900">
                                    Cheque Payment
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Payment Gateway Integration</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                            label="Razorpay Key ID"
                            name="razorpayKeyId"
                            value={formData.razorpayKeyId || ''}
                            onChange={handleInputChange}
                            placeholder="rzp_test_xxxxxxxx"
                        />
                        <Input
                            label="PayPal Client ID"
                            name="paypalClientId"
                            value={formData.paypalClientId || ''}
                            onChange={handleInputChange}
                            placeholder="Axxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        />
                        <Input
                            label="Stripe Publishable Key"
                            name="stripePublishableKey"
                            value={formData.stripePublishableKey || ''}
                            onChange={handleInputChange}
                            placeholder="pk_test_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                        />
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Late Payment Settings</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Input
                            label="Late Fee (%)"
                            name="lateFeePercentage"
                            type="number"
                            value={formData.lateFeePercentage || '0'}
                            onChange={handleInputChange}
                            placeholder="0"
                        />
                        <Input
                            label="Grace Period (days)"
                            name="gracePeriodDays"
                            type="number"
                            value={formData.gracePeriodDays || '0'}
                            onChange={handleInputChange}
                            placeholder="0"
                        />
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('PaymentSettings component error:', error);
        return null;
    }
}