const useRegisteredTemplate = (templateName) => {
    const [Template, setTemplate] = React.useState(null);

    React.useEffect(() => {
        window.templateRegistry.get(templateName, (component) => {
            setTemplate(() => component);
        });
    }, [templateName]);

    return Template;
};

function TemplatePreview({ type, companyInfo, onClose }) {
    const templateName = `${type.charAt(0).toUpperCase() + type.slice(1)}Template`;
    const TemplateComponent = useRegisteredTemplate(templateName);
    const sampleData = getSampleData(type);

    const renderContent = () => {
        if (!TemplateComponent) {
            return <div>Loading Preview...</div>;
        }
        return <TemplateComponent data={sampleData} companyInfo={companyInfo} settings={companyInfo} />;
    };

    return (
        <div className="w-full h-full">
            <div className="mb-4 flex justify-between items-center">
                <h3 className="text-lg font-semibold capitalize">{type} Preview</h3>
                <button
                    onClick={onClose}
                    className="text-gray-500 hover:text-gray-700"
                >
                    <i className="fas fa-times"></i>
                </button>
            </div>
            <div className="border rounded-lg overflow-auto max-h-96">
                {renderContent()}
            </div>
        </div>
    );
}

function getSampleData(type) {
    const baseData = {
        invoiceNumber: 'INV-2025-001',
        createdAt: new Date().toISOString(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        status: 'sent',
        customer: 'sample-customer',
        items: [
            {
                description: 'Web Development Services',
                quantity: 1,
                price: 2500,
                details: 'Custom website development with responsive design'
            },
            {
                description: 'SEO Optimization',
                quantity: 1,
                price: 800,
                details: 'On-page and technical SEO improvements'
            }
        ],
        subtotal: 3300,
        tax: 330,
        taxRate: 10,
        total: 3630,
        balance: 3630,
        amountPaid: 0,
        paymentMethod: 'Bank Transfer',
        notes: 'Thank you for your business!',
        terms: 'Payment due within 30 days. Late payments may incur additional charges.'
    };

    switch (type) {
        case 'quotation':
            return {
                ...baseData,
                quotationNumber: 'QT-2025-001',
                validUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
            };
        case 'contract':
            return {
                ...baseData,
                contractNumber: 'CT-2025-001',
                startDate: new Date().toISOString(),
                endDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
            };
        default:
            return baseData;
    }
}

window.TemplatePreview = TemplatePreview;