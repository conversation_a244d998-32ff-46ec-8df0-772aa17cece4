function TemplateSettings({ formData, handleTemplateChange, handleMarginChange, handleOpenPreview }) {
    try {
        const templates = formData.templates || {
            paperSize: 'a4',
            orientation: 'portrait',
            margins: { top: 15, right: 15, bottom: 15, left: 15 },
            invoiceTemplate: 'modern',
            quotationTemplate: 'classic',
            contractTemplate: 'professional',
            headerColor: '#3b82f6',
            accentColor: '#1e3a8a',
            fontFamily: 'Segoe UI, sans-serif',
            fontSize: 'medium',
            showLogo: true,
            showSignature: true,
            showWatermark: true,
            invoicePrefix: 'INV',
            quotationPrefix: 'QUO',
            contractPrefix: 'CON',
            showBankDetails: true,
            showPaymentTerms: true,
            footerText: 'Thank you for your business!'
        };

        const margins = templates.margins || { top: 15, right: 15, bottom: 15, left: 15 };

        return (
            <div className="space-y-8">
                <h2 className="text-xl font-bold text-gray-800 mb-6">Template Settings</h2>
                
                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Document Numbering</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Invoice Prefix</label>
                            <input
                                type="text"
                                name="invoicePrefix"
                                value={templates.invoicePrefix || 'INV'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="INV"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Quotation Prefix</label>
                            <input
                                type="text"
                                name="quotationPrefix"
                                value={templates.quotationPrefix || 'QUO'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="QUO"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Contract Prefix</label>
                            <input
                                type="text"
                                name="contractPrefix"
                                value={templates.contractPrefix || 'CON'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="CON"
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Colors & Typography</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Header Color</label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="color"
                                    name="headerColor"
                                    value={templates.headerColor || '#3b82f6'}
                                    onChange={handleTemplateChange}
                                    className="h-8 w-8 border border-gray-300 rounded p-0"
                                />
                                <input
                                    type="text"
                                    name="headerColor"
                                    value={templates.headerColor || '#3b82f6'}
                                    onChange={handleTemplateChange}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Accent Color</label>
                            <div className="flex items-center space-x-2">
                                <input
                                    type="color"
                                    name="accentColor"
                                    value={templates.accentColor || '#1e3a8a'}
                                    onChange={handleTemplateChange}
                                    className="h-8 w-8 border border-gray-300 rounded p-0"
                                />
                                <input
                                    type="text"
                                    name="accentColor"
                                    value={templates.accentColor || '#1e3a8a'}
                                    onChange={handleTemplateChange}
                                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                />
                            </div>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Font Family</label>
                            <select
                                name="fontFamily"
                                value={templates.fontFamily || 'Segoe UI, sans-serif'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="Segoe UI, sans-serif">Segoe UI (Default)</option>
                                <option value="Arial, sans-serif">Arial</option>
                                <option value="Helvetica, sans-serif">Helvetica</option>
                                <option value="Times New Roman, serif">Times New Roman</option>
                                <option value="Georgia, serif">Georgia</option>
                                <option value="Verdana, sans-serif">Verdana</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                            <select
                                name="fontSize"
                                value={templates.fontSize || 'medium'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                            >
                                <option value="small">Small</option>
                                <option value="medium">Medium (Default)</option>
                                <option value="large">Large</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Display Options</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="showLogo"
                                    id="showLogo"
                                    checked={templates.showLogo !== false}
                                    onChange={handleTemplateChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="showLogo" className="ml-2 block text-sm text-gray-900">
                                    Show Company Logo
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="showBankDetails"
                                    id="showBankDetails"
                                    checked={templates.showBankDetails !== false}
                                    onChange={handleTemplateChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="showBankDetails" className="ml-2 block text-sm text-gray-900">
                                    Show Bank Details on Invoices
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="showPaymentTerms"
                                    id="showPaymentTerms"
                                    checked={templates.showPaymentTerms !== false}
                                    onChange={handleTemplateChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="showPaymentTerms" className="ml-2 block text-sm text-gray-900">
                                    Show Payment Terms
                                </label>
                            </div>
                        </div>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="showSignature"
                                    id="showSignature"
                                    checked={templates.showSignature !== false}
                                    onChange={handleTemplateChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="showSignature" className="ml-2 block text-sm text-gray-900">
                                    Show Signature Line
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    name="showWatermark"
                                    id="showWatermark"
                                    checked={templates.showWatermark !== false}
                                    onChange={handleTemplateChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="showWatermark" className="ml-2 block text-sm text-gray-900">
                                    Show Status Watermark (e.g., DRAFT, PAID)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Custom Text</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Footer Text</label>
                            <input
                                type="text"
                                name="footerText"
                                value={templates.footerText || 'Thank you for your business!'}
                                onChange={handleTemplateChange}
                                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                placeholder="Thank you for your business!"
                            />
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Document Preview</h3>
                    <div className="flex flex-wrap gap-3">
                        <button
                            type="button"
                            onClick={() => handleOpenPreview('invoice')}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            <i className="fas fa-file-invoice mr-2"></i>
                            Invoice Preview
                        </button>
                        <button
                            type="button"
                            onClick={() => handleOpenPreview('quotation')}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            <i className="fas fa-file-invoice-dollar mr-2"></i>
                            Quotation Preview
                        </button>
                        <button
                            type="button"
                            onClick={() => handleOpenPreview('contract')}
                            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                            <i className="fas fa-file-contract mr-2"></i>
                            Contract Preview
                        </button>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('TemplateSettings component error:', error);
        return null;
    }
}