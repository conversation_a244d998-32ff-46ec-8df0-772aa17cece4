function ContractTemplate({ contract, companyInfo, settings = {} }) {
// ... existing code ...
        const getCurrencySymbol = (currency) => {
            const symbols = {
                USD: '$', EUR: '€', GBP: '£', CAD: '$', AUD: '$', INR: '₹'
            };
            return symbols[currency] || '$';
        };

        const contractData = contract?.objectData || contract || {};
        const [customerData, setCustomerData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            async function fetchCustomerData() {
                try {
                    if (typeof contractData.customer === 'string') {
                        const customer = await trickleGetObject('customer', contractData.customer);
                        setCustomerData(customer?.objectData || null);
                    } else if (contractData.customer) {
                        setCustomerData(contractData.customer);
                    } else {
                        setCustomerData({
                            name: contractData.customerName || 'Sample Customer',
                            company: contractData.customerCompany || '',
                            address: contractData.customerAddress || '123 Business Street',
                            email: contractData.customerEmail || '<EMAIL>',
                            phone: contractData.customerPhone || '******-567-8900'
                        });
                    }
                } catch (error) {
                    console.log('Customer not found, using fallback data');
                    setCustomerData({
                        name: contractData.customerName || 'Sample Customer',
                        company: contractData.customerCompany || '',
                        address: contractData.customerAddress || '123 Business Street',
                        email: contractData.customerEmail || '<EMAIL>',
                        phone: contractData.customerPhone || '******-567-8900'
                    });
                } finally {
                    setLoading(false);
                }
            }
            
            fetchCustomerData();
        }, [contract]);

        const templateStyles = settings?.templates || {};
        const companySettings = settings?.company || companyInfo || {};
        const currency = companySettings?.currency || companySettings?.companyCurrency || 'USD';
        const currencySymbol = getCurrencySymbol(currency);
        const getStatusColor = (status) => {
            const colors = {
                draft: '#6b7280',
                pending: '#f59e0b',
                signed: '#10b981',
                expired: '#ef4444'
            };
            return colors[status?.toLowerCase()] || '#6b7280';
        };

        try {
            return (
                <div className="max-w-5xl mx-auto bg-white shadow-xl" data-name="contract-template">
                {/* Header */}
                <div className="bg-gradient-to-r from-purple-600 to-purple-800 text-white px-8 py-8">
                    <div className="flex justify-between items-start">
                        <div>
                            <h1 className="text-4xl font-bold mb-2">CONTRACT</h1>
                            <p className="text-purple-100 text-lg">Service Agreement</p>
                        </div>
                        <div className="text-right">
                            {(companySettings?.companyLogo || companySettings?.logo) && templateStyles?.showLogo && (
                                <img src={companySettings.companyLogo || companySettings.logo} alt="Company Logo" className="h-12 ml-auto mb-2" />
                            )}
                            <h2 className="text-2xl font-bold mb-2">{companySettings?.companyName || companySettings?.name || 'Your Company'}</h2>
                            <div className="text-purple-100 text-sm">
                                <div>{companySettings?.companyAddress || companySettings?.address || '123 Business Street'}</div>
                                <div>{companySettings?.companyPhone || companySettings?.phone || '******-567-8900'}</div>
                                <div>{companySettings?.companyEmail || companySettings?.email || '<EMAIL>'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Contract Info */}
                <div className="bg-gray-50 px-8 py-4 border-b">
                    <div className="flex justify-between items-center">
                        <div className="flex space-x-8">
                            <div><span className="font-semibold">Contract #:</span> <span className="text-purple-600 font-bold">{contractData.contractNumber || `${templateStyles?.contractPrefix || 'CON'}-001`}</span></div>
                            <div><span className="font-semibold">Start Date:</span> {formatDate(contractData.startDate)}</div>
                            <div><span className="font-semibold">End Date:</span> {formatDate(contractData.endDate)}</div>
                        </div>
                        <div className="px-4 py-2 rounded-full text-white text-sm font-bold"
                             style={{ backgroundColor: getStatusColor(contractData.status) }}>
                            {(contractData.status || 'draft').toUpperCase()}
                        </div>
                    </div>
                </div>

                {/* Parties */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 px-8 py-8">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4">Service Provider</h3>
                        <div className="space-y-2">
                            <div className="font-bold text-xl text-gray-800">{companySettings?.companyName || companySettings?.name || 'Your Company'}</div>
                            <div className="text-gray-600">{companySettings?.companyAddress || companySettings?.address || '123 Business Street'}</div>
                            <div><span className="font-semibold">Email:</span> {companySettings?.companyEmail || companySettings?.email || '<EMAIL>'}</div>
                            <div><span className="font-semibold">Phone:</span> {companySettings?.companyPhone || companySettings?.phone || '******-567-8900'}</div>
                        </div>
                    </div>

                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4">Client</h3>
                        {loading ? (
                            <div className="text-gray-500">Loading...</div>
                        ) : (
                            <div className="space-y-2">
                                <div className="font-bold text-xl text-gray-800">{customerData?.name}</div>
                                {customerData?.company && <div className="text-gray-600">{customerData.company}</div>}
                                <div className="text-gray-600">{customerData?.address}</div>
                                <div><span className="font-semibold">Email:</span> {customerData?.email}</div>
                                <div><span className="font-semibold">Phone:</span> {customerData?.phone}</div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Contract Details */}
                <div className="px-8 py-6">
                    <div className="bg-gray-50 rounded-lg p-6">
                        <h3 className="text-xl font-bold text-gray-800 mb-4">Contract Terms</h3>
                        <div className="prose max-w-none">
                            <p className="text-gray-700 leading-relaxed">
                                {contractData.description || 'This contract outlines the terms and conditions for the services to be provided.'}
                            </p>
                            {contractData.terms && (
                                <div className="mt-4">
                                    <h4 className="font-semibold text-gray-800 mb-2">Additional Terms:</h4>
                                    <p className="text-gray-700">{contractData.terms}</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Contract Value */}
                <div className="px-8 py-6">
                    <div className="max-w-md ml-auto bg-purple-50 rounded-lg p-6">
                        <div className="text-center">
                            <h4 className="font-bold text-purple-800 mb-2">Contract Value</h4>
                            <div className="text-3xl font-bold text-purple-600">{currencySymbol}{contractData.value || 0}</div>
                        </div>
                    </div>
                </div>

                {/* Signatures */}
                <div className="px-8 py-8 border-t">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div className="text-center">
                            <div className="border-t border-gray-400 pt-2 mt-16">
                                <p className="font-semibold">Service Provider Signature</p>
                                <p className="text-sm text-gray-600">Date: _______________</p>
                            </div>
                        </div>
                        <div className="text-center">
                            <div className="border-t border-gray-400 pt-2 mt-16">
                                <p className="font-semibold">Client Signature</p>
                                <p className="text-sm text-gray-600">Date: _______________</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="bg-gradient-to-r from-purple-600 to-purple-800 text-white px-8 py-6 text-center">
                    <p className="text-lg font-semibold">Thank you for choosing our services!</p>
                </div>
            </div>
        );
    } catch (error) {
        console.error('ContractTemplate component error:', error);
        return (
            <div className="max-w-4xl mx-auto bg-white p-8 text-center">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Contract Template Error</h3>
                <p className="text-gray-600">Please try again.</p>
            </div>
        );
    }
}
window.templateRegistry.register('ContractTemplate', ContractTemplate);