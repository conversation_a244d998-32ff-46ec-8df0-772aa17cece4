// Professional Industry-Standard Invoice Template
function InvoiceTemplate({ data = {}, companyInfo = {}, settings = {} }) {
// ... existing code ...
        const getCurrencySymbol = (currency) => {
            const symbols = {
                USD: '$', EUR: '€', GBP: '£', CAD: '$', AUD: '$', INR: '₹', 
                JPY: '¥', CHF: 'CHF', CNY: '¥', KRW: '₩', SGD: '$'
            };
            return symbols[currency] || '$';
        };

        // Ensure data is properly structured
        const invoiceData = data?.objectData || data || {};
        
        const [customerData, setCustomerData] = React.useState({
            name: invoiceData.customerName || 'Sample Customer',
            company: invoiceData.customerCompany || '',
            address: invoiceData.customerAddress || '123 Business Street, City, State',
            email: invoiceData.customerEmail || '<EMAIL>',
            phone: invoiceData.customerPhone || '******-567-8900',
            gst: invoiceData.customerGST || ''
        });
        const [loading, setLoading] = React.useState(false);

        React.useEffect(() => {
            if (invoiceData.customer) {
                fetchCustomerData();
            }
        }, [invoiceData.customer]);

        const fetchCustomerData = async () => {
            try {
                setLoading(true);
                if (invoiceData.customer && invoiceData.customer !== 'sample-customer') {
                    const response = await trickleGetObject('customer', invoiceData.customer);
                    setCustomerData(response?.objectData || customerData);
                }
            } catch (error) {
                console.log('Customer not found, using fallback data');
            } finally {
                setLoading(false);
            }
        };

        const templateStyles = settings?.templates || {};
        const companySettings = settings?.company || companyInfo || {};
        const currency = companySettings?.currency || companySettings?.companyCurrency || 'USD';
        const currencySymbol = getCurrencySymbol(currency);

        const getStatusColor = (status) => {
            const statusColors = {
                draft: '#64748b',
                sent: '#0ea5e9',
                paid: '#22c55e',
                overdue: '#ef4444',
                cancelled: '#6b7280',
                partial: '#f59e0b'
            };
            return statusColors[status?.toLowerCase()] || '#64748b';
        };

        const getStatusBadge = (status) => {
            const statusConfig = {
                draft: { bg: '#f1f5f9', text: '#475569', icon: '📝' },
                sent: { bg: '#e0f2fe', text: '#0369a1', icon: '📤' },
                paid: { bg: '#dcfce7', text: '#166534', icon: '✅' },
                overdue: { bg: '#fee2e2', text: '#dc2626', icon: '⚠️' },
                cancelled: { bg: '#f3f4f6', text: '#4b5563', icon: '❌' },
                partial: { bg: '#fef3c7', text: '#d97706', icon: '⏳' }
            };
            const config = statusConfig[status?.toLowerCase()] || statusConfig.draft;
            return config;
        };

        const statusBadge = getStatusBadge(invoiceData.status);

        try {
            return (
                <div className="max-w-4xl mx-auto bg-white shadow-2xl border border-gray-100" data-name="invoice-template">
                {/* Modern Header with Clean Design */}
                <div className="relative overflow-hidden">
                    {/* Background Pattern */}
                    <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-blue-50"></div>
                    <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-blue-100/30 to-transparent rounded-full -translate-y-32 translate-x-32"></div>
                    
                    <div className="relative px-8 py-8">
                        <div className="flex justify-between items-start">
                            {/* Company Info */}
                            <div className="flex-1">
                                {(companySettings?.companyLogo || companySettings?.logo) && templateStyles?.showLogo && (
                                    <div className="mb-4">
                                        <img src={companySettings.companyLogo || companySettings.logo} 
                                             alt="Company Logo" 
                                             className="h-16 w-auto object-contain" />
                                    </div>
                                )}
                                <div className="space-y-1">
                                    <h2 className="text-2xl font-bold text-gray-900">
                                        {companySettings?.companyName || companySettings?.name || 'Your Company'}
                                    </h2>
                                    <div className="text-sm text-gray-600 space-y-0.5">
                                        <div>{companySettings?.companyAddress || companySettings?.address || '123 Business Street'}</div>
                                        <div>
                                            {companySettings?.companyCity || companySettings?.city || 'City'}, {' '}
                                            {companySettings?.companyState || companySettings?.state || 'State'} {' '}
                                            {companySettings?.companyZip || companySettings?.zipCode || '12345'}
                                            {companySettings?.companyCountry && `, ${companySettings.companyCountry}`}
                                        </div>
                                        <div className="flex items-center space-x-4 mt-2">
                                            <span className="flex items-center">
                                                <svg className="w-3 h-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                                </svg>
                                                {companySettings?.companyPhone || companySettings?.phone || '******-567-8900'}
                                            </span>
                                            <span className="flex items-center">
                                                <svg className="w-3 h-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                                </svg>
                                                {companySettings?.companyEmail || companySettings?.email || '<EMAIL>'}
                                            </span>
                                        </div>
                                        {(companySettings?.companyWebsite || companySettings?.website) && (
                                            <div className="flex items-center mt-1">
                                                <svg className="w-3 h-3 mr-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.559-.499-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.559.499.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.497-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd"/>
                                                </svg>
                                                {companySettings.companyWebsite || companySettings.website}
                                            </div>
                                        )}
                                    </div>
                                    {(companySettings?.companyGST || companySettings?.companyRegistration) && (
                                        <div className="mt-3 pt-2 border-t border-gray-200 text-xs text-gray-500 space-y-0.5">
                                            {companySettings?.companyGST && <div>GST/Tax ID: {companySettings.companyGST}</div>}
                                            {companySettings?.companyRegistration && <div>Registration: {companySettings.companyRegistration}</div>}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Invoice Title & Status */}
                            <div className="text-right">
                                <div className="mb-4">
                                    <h1 className="text-4xl font-bold text-gray-900 mb-1">INVOICE</h1>
                                    <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium"
                                         style={{ backgroundColor: statusBadge.bg, color: statusBadge.text }}>
                                        <span className="mr-1">{statusBadge.icon}</span>
                                        {(invoiceData.status || 'draft').toUpperCase()}
                                    </div>
                                </div>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">Invoice Number:</span>
                                        <span className="font-bold text-gray-900">
                                            {invoiceData.invoiceNumber || `${templateStyles?.invoicePrefix || 'INV'}-001`}
                                        </span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">Issue Date:</span>
                                        <span className="text-gray-900">{formatDate(invoiceData.createdAt)}</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-gray-600 font-medium">Due Date:</span>
                                        <span className="text-gray-900 font-semibold">{formatDate(invoiceData.dueDate)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Customer Information & Payment Summary */}
                <div className="px-8 py-6 bg-gray-50/50">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        {/* Bill To */}
                        <div className="lg:col-span-2">
                            <div className="bg-white rounded-lg border border-gray-200 p-6">
                                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                    <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd"/>
                                    </svg>
                                    Bill To
                                </h3>
                                {loading ? (
                                    <div className="text-gray-500 animate-pulse">Loading customer information...</div>
                                ) : (
                                    <div className="space-y-2">
                                        <div className="font-bold text-lg text-gray-900">{customerData.name}</div>
                                        {customerData.company && (
                                            <div className="text-gray-700 font-medium">{customerData.company}</div>
                                        )}
                                        <div className="text-gray-600 text-sm leading-relaxed">
                                            {customerData.address}
                                        </div>
                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-3 pt-3 border-t border-gray-100">
                                            <div className="flex items-center text-sm">
                                                <svg className="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                                                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                                                </svg>
                                                <span className="text-gray-600">{customerData.email}</span>
                                            </div>
                                            <div className="flex items-center text-sm">
                                                <svg className="w-4 h-4 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                                                </svg>
                                                <span className="text-gray-600">{customerData.phone}</span>
                                            </div>
                                        </div>
                                        {customerData.gst && (
                                            <div className="text-xs text-gray-500 mt-2">
                                                GST/Tax ID: {customerData.gst}
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Payment Summary */}
                        <div>
                            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
                                <h3 className="text-lg font-semibold text-blue-900 mb-4 flex items-center">
                                    <svg className="w-5 h-5 mr-2 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z"/>
                                    </svg>
                                    Payment Summary
                                </h3>
                                <div className="space-y-3">
                                    <div className="flex justify-between items-center text-sm">
                                        <span className="text-blue-700">Payment Method:</span>
                                        <span className="font-medium text-blue-900">
                                            {invoiceData.paymentMethod || 'Bank Transfer'}
                                        </span>
                                    </div>
                                    {templateStyles?.showPaymentTerms && companySettings?.companyPaymentTerms && (
                                        <div className="flex justify-between items-center text-sm">
                                            <span className="text-blue-700">Payment Terms:</span>
                                            <span className="font-medium text-blue-900">
                                                {companySettings.companyPaymentTerms}
                                            </span>
                                        </div>
                                    )}
                                    <div className="border-t border-blue-200 pt-3 space-y-2">
                                        <div className="flex justify-between items-center">
                                            <span className="text-blue-700 font-medium">Total Amount:</span>
                                            <span className="text-xl font-bold text-blue-900">
                                                {currencySymbol}{(invoiceData.total || 0).toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center text-sm">
                                            <span className="text-green-700">Amount Paid:</span>
                                            <span className="font-semibold text-green-800">
                                                {currencySymbol}{(invoiceData.amountPaid || 0).toLocaleString()}
                                            </span>
                                        </div>
                                        <div className="flex justify-between items-center text-sm border-t border-blue-200 pt-2">
                                            <span className="font-bold text-red-700">Balance Due:</span>
                                            <span className="text-lg font-bold text-red-800">
                                                {currencySymbol}{((invoiceData.total || 0) - (invoiceData.amountPaid || 0)).toLocaleString()}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                {templateStyles?.showBankDetails && companySettings?.bankDetails && (
                                    <div className="mt-4 pt-4 border-t border-blue-200">
                                        <h4 className="font-semibold text-blue-900 mb-2 text-sm">Bank Details</h4>
                                        <div className="text-xs text-blue-800 whitespace-pre-line bg-white/50 p-2 rounded">
                                            {companySettings.bankDetails}
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Items Table */}
                <div className="px-8 py-6">
                    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                        <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                            <h3 className="text-lg font-semibold text-gray-900">Invoice Items</h3>
                        </div>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50 border-b border-gray-200">
                                    <tr>
                                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-12">
                                            #
                                        </th>
                                        <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th className="px-6 py-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-20">
                                            Qty
                                        </th>
                                        <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider w-24">
                                            Rate
                                        </th>
                                        <th className="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider w-28">
                                            Amount
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="divide-y divide-gray-100">
                                    {(invoiceData.items || []).map((item, index) => (
                                        <tr key={index} className="hover:bg-gray-50/50 transition-colors">
                                            <td className="px-6 py-4 text-sm text-gray-500 font-medium">
                                                {String(index + 1).padStart(2, '0')}
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm font-semibold text-gray-900">
                                                    {item.name || item.description}
                                                </div>
                                                {item.details && (
                                                    <div className="text-xs text-gray-500 mt-1">
                                                        {item.details}
                                                    </div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 text-center text-sm font-medium text-gray-900">
                                                {item.quantity}
                                            </td>
                                            <td className="px-6 py-4 text-right text-sm text-gray-900">
                                                {formatCurrency(item.rate, currency)}
                                            </td>
                                            <td className="px-6 py-4 text-right text-sm font-bold text-gray-900">
                                                {formatCurrency(item.total, currency)}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                {/* Totals Section */}
                <div className="px-8 py-6 bg-gray-50/50">
                    <div className="max-w-md ml-auto">
                        <div className="bg-white rounded-lg border border-gray-200 p-6">
                            <h3 className="text-lg font-semibold text-gray-900 mb-4">Invoice Total</h3>
                            <div className="space-y-3">
                                <div className="flex justify-between items-center text-sm">
                                    <span className="text-gray-600">Subtotal:</span>
                                    <span className="font-medium text-gray-900">
                                        {formatCurrency(invoiceData.subtotal, currency)}
                                    </span>
                                </div>
                                {invoiceData.discount > 0 && (
                                    <div className="flex justify-between items-center text-sm">
                                        <span className="text-gray-600">Discount:</span>
                                        <span className="font-medium text-green-600">
                                            -{formatCurrency(invoiceData.discount, currency)}
                                        </span>
                                    </div>
                                )}
                                {invoiceData.tax > 0 && (
                                    <div className="flex justify-between items-center text-sm">
                                        <span className="text-gray-600">Tax:</span>
                                        <span className="font-medium text-gray-900">
                                            {formatCurrency(invoiceData.tax, currency)}
                                        </span>
                                    </div>
                                )}
                                <div className="border-t border-gray-200 pt-3">
                                    <div className="flex justify-between items-center">
                                        <span className="text-lg font-bold text-gray-900">Total:</span>
                                        <span className="text-2xl font-bold text-blue-600">
                                            {formatCurrency(invoiceData.total, currency)}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Notes Section */}
                {(invoiceData.notes || templateStyles?.defaultNotes) && (
                    <div className="px-8 py-6 border-t border-gray-200">
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 className="font-semibold text-yellow-800 mb-2 flex items-center">
                                <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd"/>
                                </svg>
                                Notes
                            </h4>
                            <p className="text-sm text-yellow-700 leading-relaxed">
                                {invoiceData.notes || templateStyles?.defaultNotes}
                            </p>
                        </div>
                    </div>
                )}

                {/* Terms & Conditions */}
                {(templateStyles?.showTerms && companySettings?.terms) && (
                    <div className="px-8 py-6 border-t border-gray-200">
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h4 className="font-semibold text-gray-800 mb-2">Terms & Conditions</h4>
                            <div className="text-xs text-gray-600 leading-relaxed whitespace-pre-line">
                                {companySettings.terms}
                            </div>
                        </div>
                    </div>
                )}

                {/* Signature Section */}
                {templateStyles?.showSignature && (
                    <div className="px-8 py-8 border-t border-gray-200">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                            <div className="text-center">
                                <div className="h-16 border-b-2 border-gray-300 mb-3"></div>
                                <div className="space-y-1">
                                    <p className="font-semibold text-gray-800">Authorized Signature</p>
                                    <p className="text-sm text-gray-600">
                                        {companySettings?.authorizedName || 'Company Representative'}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        Date: {new Date().toLocaleDateString()}
                                    </p>
                                </div>
                            </div>
                            <div className="text-center">
                                <div className="h-16 border-b-2 border-gray-300 mb-3"></div>
                                <div className="space-y-1">
                                    <p className="font-semibold text-gray-800">Customer Signature</p>
                                    <p className="text-sm text-gray-600">Received in good condition</p>
                                    <p className="text-xs text-gray-500">Date: _______________</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Professional Footer */}
                <div className="bg-gradient-to-r from-gray-800 to-gray-900 text-white px-8 py-6">
                    <div className="text-center">
                        <p className="text-lg font-medium mb-2">
                            {templateStyles?.footerText || 'Thank you for your business!'}
                        </p>
                        <div className="flex justify-center items-center space-x-6 text-sm text-gray-300">
                            <span>Invoice generated on {new Date().toLocaleDateString()}</span>
                            <span>•</span>
                            <span>Questions? Contact us at {companySettings?.companyEmail || companySettings?.email || '<EMAIL>'}</span>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('InvoiceTemplate component error:', error);
        return (
            <div className="max-w-4xl mx-auto bg-white p-8 text-center border border-red-200 rounded-lg">
                <div className="text-red-600 mb-4">
                    <svg className="w-12 h-12 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"/>
                    </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Invoice Template Error</h3>
                <p className="text-gray-600">Unable to load invoice template. Please try again or contact support.</p>
            </div>
        );
    }
}
window.templateRegistry.register('InvoiceTemplate', InvoiceTemplate);