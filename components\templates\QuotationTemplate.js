function QuotationTemplate({ quotation, companyInfo, settings = {} }) {
// ... existing code ...
        const getCurrencySymbol = (currency) => {
            const symbols = {
                USD: '$', EUR: '€', GBP: '£', CAD: '$', AUD: '$', INR: '₹'
            };
            return symbols[currency] || '$';
        };

        const quotationData = quotation?.objectData || quotation || {};
        const [customerData, setCustomerData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            async function fetchCustomerData() {
                try {
                    if (typeof quotationData.customer === 'string') {
                        const customer = await trickleGetObject('customer', quotationData.customer);
                        setCustomerData(customer?.objectData || null);
                    } else if (quotationData.customer) {
                        setCustomerData(quotationData.customer);
                    } else {
                        setCustomerData({
                            name: quotationData.customerName || 'Sample Customer',
                            company: quotationData.customerCompany || '',
                            address: quotationData.customerAddress || '123 Business Street',
                            email: quotationData.customerEmail || '<EMAIL>',
                            phone: quotationData.customerPhone || '******-567-8900'
                        });
                    }
                } catch (error) {
                    console.log('Customer not found, using fallback data');
                    setCustomerData({
                        name: quotationData.customerName || 'Sample Customer',
                        company: quotationData.customerCompany || '',
                        address: quotationData.customerAddress || '123 Business Street',
                        email: quotationData.customerEmail || '<EMAIL>',
                        phone: quotationData.customerPhone || '******-567-8900'
                    });
                } finally {
                    setLoading(false);
                }
            }
            
            fetchCustomerData();
        }, [quotation]);

        const templateStyles = settings?.templates || {};
        const companySettings = settings?.company || companyInfo || {};
        const currency = companySettings?.currency || companySettings?.companyCurrency || 'USD';
        const currencySymbol = getCurrencySymbol(currency);
        const isDraft = !quotationData.status || quotationData.status.toLowerCase() === 'draft';
        const isAccepted = quotationData.status && quotationData.status.toLowerCase() === 'accepted';

        const getStatusColor = (status) => {
            const colors = {
                draft: '#6b7280',
                sent: '#3b82f6',
                accepted: '#10b981',
                rejected: '#ef4444'
            };
            return colors[status?.toLowerCase()] || '#6b7280';
        };

        try {
            return (
                <div className="max-w-5xl mx-auto bg-white shadow-xl" data-name="quotation-template">
                {/* Header */}
                <div className="bg-gradient-to-r from-green-600 to-green-800 text-white px-8 py-8">
                    <div className="flex justify-between items-start">
                        <div>
                            <h1 className="text-4xl font-bold mb-2">QUOTATION</h1>
                            <p className="text-green-100 text-lg">Professional Price Quote</p>
                        </div>
                        <div className="text-right">
                            {(companySettings?.companyLogo || companySettings?.logo) && templateStyles?.showLogo && (
                                <img src={companySettings.companyLogo || companySettings.logo} alt="Company Logo" className="h-12 ml-auto mb-2" />
                            )}
                            <h2 className="text-2xl font-bold mb-2">{companySettings?.companyName || companySettings?.name || 'Your Company'}</h2>
                            <div className="text-green-100 text-sm">
                                <div>{companySettings?.companyAddress || companySettings?.address || '123 Business Street'}</div>
                                <div>{companySettings?.companyPhone || companySettings?.phone || '******-567-8900'}</div>
                                <div>{companySettings?.companyEmail || companySettings?.email || '<EMAIL>'}</div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Quote Info */}
                <div className="bg-gray-50 px-8 py-4 border-b">
                    <div className="flex justify-between items-center">
                        <div className="flex space-x-8">
                            <div><span className="font-semibold">Quote #:</span> <span className="text-green-600 font-bold">{quotationData.quotationNumber || `${templateStyles?.quotationPrefix || 'QUO'}-001`}</span></div>
                            <div><span className="font-semibold">Date:</span> {formatDate(quotationData.createdAt)}</div>
                            <div><span className="font-semibold">Valid Until:</span> {formatDate(quotationData.validUntil)}</div>
                        </div>
                        <div className="px-4 py-2 rounded-full text-white text-sm font-bold"
                             style={{ backgroundColor: getStatusColor(quotationData.status) }}>
                            {(quotationData.status || 'draft').toUpperCase()}
                        </div>
                    </div>
                </div>

                {/* Customer Info */}
                <div className="px-8 py-8">
                    <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 className="text-lg font-bold text-gray-800 mb-4">Quote For:</h3>
                        {loading ? (
                            <div className="text-gray-500">Loading...</div>
                        ) : (
                            <div className="space-y-2">
                                <div className="font-bold text-xl text-gray-800">{customerData?.name}</div>
                                {customerData?.company && <div className="text-gray-600">{customerData.company}</div>}
                                <div className="text-gray-600">{customerData?.address}</div>
                                <div><span className="font-semibold">Email:</span> {customerData?.email}</div>
                                <div><span className="font-semibold">Phone:</span> {customerData?.phone}</div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Items */}
                <div className="px-8 py-6">
                    <table className="w-full border border-gray-200 rounded-lg overflow-hidden">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-4 text-left">#</th>
                                <th className="px-6 py-4 text-left">Description</th>
                                <th className="px-6 py-4 text-center">Qty</th>
                                <th className="px-6 py-4 text-right">Rate</th>
                                <th className="px-6 py-4 text-right">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {(quotationData.items || []).map((item, index) => (
                                <tr key={index} className="border-t">
                                    <td className="px-6 py-4">{index + 1}</td>
                                    <td className="px-6 py-4 font-semibold">{item.name || item.description}</td>
                                    <td className="px-6 py-4 text-center">{item.quantity}</td>
                                    <td className="px-6 py-4 text-right">{formatCurrency(item.rate, currency)}</td>
                                    <td className="px-6 py-4 text-right font-bold">{formatCurrency(item.total, currency)}</td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Total */}
                <div className="px-8 py-6">
                    <div className="max-w-md ml-auto bg-green-50 rounded-lg p-6">
                        <div className="text-center">
                            <h4 className="font-bold text-green-800 mb-2">Total Quote Amount</h4>
                            <div className="text-3xl font-bold text-green-600">{currencySymbol}{quotationData.total || 0}</div>
                        </div>
                    </div>
                </div>

                {/* Footer */}
                <div className="bg-gradient-to-r from-green-600 to-green-800 text-white px-8 py-6 text-center">
                    <p className="text-lg font-semibold">Thank you for considering our services!</p>
                </div>
            </div>
        );
    } catch (error) {
        console.error('QuotationTemplate component error:', error);
        return (
            <div className="max-w-4xl mx-auto bg-white p-8 text-center">
                <h3 className="text-lg font-semibold text-gray-800 mb-2">Quotation Template Error</h3>
                <p className="text-gray-600">Please try again.</p>
            </div>
        );
    }
}
window.templateRegistry.register('QuotationTemplate', QuotationTemplate);