<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Manager - BhaviCRM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <style>
        .loading { opacity: 0.6; pointer-events: none; }
        .status-healthy { color: #10b981; }
        .status-warning { color: #f59e0b; }
        .status-error { color: #ef4444; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Database Manager</h1>
            <p class="text-gray-600">Manage your BhaviCRM database</p>
        </div>

        <!-- Status Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-database text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Database Status</p>
                        <p id="db-status" class="text-2xl font-bold">Loading...</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-table text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Tables</p>
                        <p id="table-count" class="text-2xl font-bold">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-hdd text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Size</p>
                        <p id="db-size" class="text-2xl font-bold">-</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-orange-100 text-orange-600">
                        <i class="fas fa-activity text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Recent Activity</p>
                        <p id="recent-activity" class="text-2xl font-bold">-</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Database Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button onclick="refreshStatus()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh Status
                </button>
                <button onclick="createBackup()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-download mr-2"></i>Create Backup
                </button>
                <button onclick="testConnection()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                    <i class="fas fa-plug mr-2"></i>Test Connection
                </button>
                <button onclick="showInstallSchema()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                    <i class="fas fa-cogs mr-2"></i>Install Schema
                </button>
            </div>
        </div>

        <!-- Database Information -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Tables Information -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Tables Information</h2>
                <div id="tables-info" class="space-y-2">
                    <p class="text-gray-500">Loading table information...</p>
                </div>
            </div>

            <!-- Statistics -->
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Statistics</h2>
                <div id="statistics" class="space-y-3">
                    <p class="text-gray-500">Loading statistics...</p>
                </div>
            </div>
        </div>

        <!-- Health Check -->
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Health Check</h2>
            <div id="health-check">
                <p class="text-gray-500">Loading health information...</p>
            </div>
        </div>

        <!-- Install Schema Modal -->
        <div id="install-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Install Database Schema</h3>
                <p class="text-gray-600 mb-6">This will install or update the database schema. Existing data will be preserved.</p>
                <div class="flex justify-end space-x-4">
                    <button onclick="hideInstallSchema()" class="px-4 py-2 text-gray-600 hover:text-gray-800">Cancel</button>
                    <button onclick="installSchema()" class="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700">Install Schema</button>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-40">
            <div class="bg-white rounded-lg p-8 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Processing...</p>
            </div>
        </div>
    </div>

    <script>
        let currentStatus = null;

        // Load database status on page load
        document.addEventListener('DOMContentLoaded', function() {
            refreshStatus();
        });

        async function refreshStatus() {
            showLoading();
            try {
                const response = await fetch('api/database/status.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    currentStatus = data;
                    updateStatusDisplay(data);
                } else {
                    showError('Failed to load database status: ' + data.message);
                }
            } catch (error) {
                showError('Error loading database status: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        function updateStatusDisplay(data) {
            // Update status cards
            document.getElementById('db-status').textContent = data.health.status;
            document.getElementById('db-status').className = `text-2xl font-bold status-${data.health.status}`;
            
            document.getElementById('table-count').textContent = data.database.table_count;
            document.getElementById('db-size').textContent = data.database.total_size_mb + ' MB';
            document.getElementById('recent-activity').textContent = data.recent_activity;

            // Update tables information
            const tablesInfo = document.getElementById('tables-info');
            if (data.tables && data.tables.length > 0) {
                tablesInfo.innerHTML = data.tables.map(table => `
                    <div class="flex justify-between items-center py-2 border-b border-gray-100">
                        <span class="font-medium">${table.table_name}</span>
                        <div class="text-sm text-gray-500">
                            ${table.row_count} rows • ${table.size_mb} MB
                        </div>
                    </div>
                `).join('');
            } else {
                tablesInfo.innerHTML = '<p class="text-gray-500">No tables found</p>';
            }

            // Update statistics
            const statistics = document.getElementById('statistics');
            if (data.statistics) {
                statistics.innerHTML = Object.entries(data.statistics).map(([key, value]) => `
                    <div class="flex justify-between items-center">
                        <span class="capitalize">${key.replace('_', ' ')}</span>
                        <span class="font-bold text-blue-600">${value}</span>
                    </div>
                `).join('');
            }

            // Update health check
            const healthCheck = document.getElementById('health-check');
            let healthHtml = `<div class="flex items-center mb-3">
                <i class="fas fa-${data.health.status === 'healthy' ? 'check-circle text-green-600' : 'exclamation-triangle text-orange-600'} mr-2"></i>
                <span class="font-medium capitalize status-${data.health.status}">${data.health.status}</span>
            </div>`;
            
            if (data.health.issues && data.health.issues.length > 0) {
                healthHtml += '<div class="space-y-2">';
                healthHtml += data.health.issues.map(issue => `
                    <div class="bg-orange-50 border border-orange-200 rounded p-3 text-orange-800">
                        <i class="fas fa-exclamation-triangle mr-2"></i>${issue}
                    </div>
                `).join('');
                healthHtml += '</div>';
            }

            healthCheck.innerHTML = healthHtml;
        }

        async function testConnection() {
            showLoading();
            try {
                const response = await fetch('api/test.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    showSuccess('Database connection successful!');
                } else {
                    showError('Connection failed: ' + data.message);
                }
            } catch (error) {
                showError('Connection test failed: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        async function createBackup() {
            showLoading();
            try {
                const response = await fetch('api/database/backup.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    showSuccess(`Backup created successfully: ${data.filename} (${data.file_size} bytes)`);
                } else {
                    showError('Backup failed: ' + data.message);
                }
            } catch (error) {
                showError('Backup failed: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        function showInstallSchema() {
            document.getElementById('install-modal').classList.remove('hidden');
            document.getElementById('install-modal').classList.add('flex');
        }

        function hideInstallSchema() {
            document.getElementById('install-modal').classList.add('hidden');
            document.getElementById('install-modal').classList.remove('flex');
        }

        async function installSchema() {
            hideInstallSchema();
            showLoading();
            
            try {
                const response = await fetch('install.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    showSuccess('Database schema installed successfully!');
                    setTimeout(() => refreshStatus(), 1000);
                } else {
                    showError('Schema installation failed: ' + data.message);
                }
            } catch (error) {
                showError('Schema installation failed: ' + error.message);
            } finally {
                hideLoading();
            }
        }

        function showLoading() {
            document.getElementById('loading-overlay').classList.remove('hidden');
            document.getElementById('loading-overlay').classList.add('flex');
        }

        function hideLoading() {
            document.getElementById('loading-overlay').classList.add('hidden');
            document.getElementById('loading-overlay').classList.remove('flex');
        }

        function showSuccess(message) {
            showNotification(message, 'success');
        }

        function showError(message) {
            showNotification(message, 'error');
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-100 border border-green-200 text-green-800' : 
                'bg-red-100 border border-red-200 text-red-800'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} mr-2"></i>
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }
    </script>
</body>
</html>