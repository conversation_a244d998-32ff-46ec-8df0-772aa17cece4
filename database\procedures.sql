-- Stored Procedures for Business Management Application

DELIMITER //

-- Calculate invoice totals
CREATE PROCEDURE UpdateInvoiceTotals(IN invoice_id INT)
BEGIN
    DECLARE subtotal DECIMAL(15,2) DEFAULT 0;
    DECLARE tax_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE total DECIMAL(15,2) DEFAULT 0;
    DECLARE tax_rate DECIMAL(5,2) DEFAULT 0;
    
    -- Get tax rate
    SELECT i.tax_rate INTO tax_rate FROM invoices i WHERE i.id = invoice_id;
    
    -- Calculate subtotal
    SELECT SUM(total) INTO subtotal 
    FROM invoice_items 
    WHERE invoice_id = invoice_id;
    
    -- Calculate tax
    SET tax_amount = (subtotal * tax_rate / 100);
    SET total = subtotal + tax_amount;
    
    -- Update invoice
    UPDATE invoices 
    SET subtotal = subtotal, 
        tax_amount = tax_amount, 
        total = total,
        balance_due = total - amount_paid
    WHERE id = invoice_id;
END //

-- Calculate quotation totals
CREATE PROCEDURE UpdateQuotationTotals(IN quotation_id INT)
BEGIN
    DECLARE subtotal DECIMAL(15,2) DEFAULT 0;
    DECLARE tax_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE total DECIMAL(15,2) DEFAULT 0;
    DECLARE tax_rate DECIMAL(5,2) DEFAULT 0;
    
    -- Get tax rate
    SELECT q.tax_rate INTO tax_rate FROM quotations q WHERE q.id = quotation_id;
    
    -- Calculate subtotal
    SELECT SUM(total) INTO subtotal 
    FROM quotation_items 
    WHERE quotation_id = quotation_id;
    
    -- Calculate tax
    SET tax_amount = (subtotal * tax_rate / 100);
    SET total = subtotal + tax_amount;
    
    -- Update quotation
    UPDATE quotations 
    SET subtotal = subtotal, 
        tax_amount = tax_amount, 
        total = total
    WHERE id = quotation_id;
END //

-- Update invoice status based on payments
CREATE PROCEDURE UpdateInvoiceStatus(IN invoice_id INT)
BEGIN
    DECLARE total_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE paid_amount DECIMAL(15,2) DEFAULT 0;
    DECLARE due_date DATE;
    DECLARE new_status VARCHAR(20);
    
    -- Get invoice details
    SELECT total, amount_paid, due_date INTO total_amount, paid_amount, due_date
    FROM invoices WHERE id = invoice_id;
    
    -- Determine status
    IF paid_amount >= total_amount THEN
        SET new_status = 'paid';
    ELSEIF paid_amount > 0 THEN
        SET new_status = 'partial';
    ELSEIF due_date < CURDATE() THEN
        SET new_status = 'overdue';
    ELSE
        SET new_status = 'sent';
    END IF;
    
    -- Update invoice status
    UPDATE invoices SET status = new_status WHERE id = invoice_id;
END //

-- Record payment and update invoice
CREATE PROCEDURE RecordPayment(
    IN invoice_id INT,
    IN payment_amount DECIMAL(15,2),
    IN payment_date DATE,
    IN payment_method VARCHAR(100),
    IN reference_number VARCHAR(255),
    IN notes TEXT
)
BEGIN
    DECLARE current_paid DECIMAL(15,2) DEFAULT 0;
    
    -- Insert payment record
    INSERT INTO payment_records (invoice_id, amount, payment_date, payment_method, reference_number, notes)
    VALUES (invoice_id, payment_amount, payment_date, payment_method, reference_number, notes);
    
    -- Update invoice amount_paid
    SELECT COALESCE(SUM(amount), 0) INTO current_paid 
    FROM payment_records WHERE invoice_id = invoice_id;
    
    UPDATE invoices 
    SET amount_paid = current_paid,
        balance_due = total - current_paid,
        paid_at = CASE WHEN current_paid >= total THEN NOW() ELSE paid_at END
    WHERE id = invoice_id;
    
    -- Update invoice status
    CALL UpdateInvoiceStatus(invoice_id);
END //

-- Generate next invoice number
CREATE FUNCTION GetNextInvoiceNumber(company_id INT, prefix VARCHAR(10)) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE current_year VARCHAR(4);
    DECLARE invoice_number VARCHAR(50);
    
    SET current_year = YEAR(CURDATE());
    
    -- Get the highest number for this year and company
    SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number, LENGTH(prefix) + 6) AS UNSIGNED)), 0) + 1
    INTO next_number
    FROM invoices 
    WHERE company_id = company_id 
    AND invoice_number LIKE CONCAT(prefix, '-', current_year, '-%');
    
    SET invoice_number = CONCAT(prefix, '-', current_year, '-', LPAD(next_number, 3, '0'));
    
    RETURN invoice_number;
END //

-- Generate next quotation number
CREATE FUNCTION GetNextQuotationNumber(company_id INT, prefix VARCHAR(10)) 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE next_number INT DEFAULT 1;
    DECLARE current_year VARCHAR(4);
    DECLARE quote_number VARCHAR(50);
    
    SET current_year = YEAR(CURDATE());
    
    -- Get the highest number for this year and company
    SELECT COALESCE(MAX(CAST(SUBSTRING(quote_number, LENGTH(prefix) + 6) AS UNSIGNED)), 0) + 1
    INTO next_number
    FROM quotations 
    WHERE company_id = company_id 
    AND quote_number LIKE CONCAT(prefix, '-', current_year, '-%');
    
    SET quote_number = CONCAT(prefix, '-', current_year, '-', LPAD(next_number, 3, '0'));
    
    RETURN quote_number;
END //

-- Log activity
CREATE PROCEDURE LogActivity(
    IN company_id INT,
    IN user_id INT,
    IN entity_type VARCHAR(50),
    IN entity_id INT,
    IN action VARCHAR(50),
    IN description TEXT,
    IN old_values TEXT,
    IN new_values TEXT,
    IN ip_address VARCHAR(45),
    IN user_agent TEXT
)
BEGIN
    INSERT INTO activity_log (
        company_id, user_id, entity_type, entity_id, action, 
        description, old_values, new_values, ip_address, user_agent
    ) VALUES (
        company_id, user_id, entity_type, entity_id, action,
        description, old_values, new_values, ip_address, user_agent
    );
END //

-- Get company statistics
CREATE PROCEDURE GetCompanyStats(IN company_id INT)
BEGIN
    SELECT 
        (SELECT COUNT(*) FROM customers WHERE company_id = company_id AND is_active = 1) as total_customers,
        (SELECT COUNT(*) FROM invoices WHERE company_id = company_id) as total_invoices,
        (SELECT COUNT(*) FROM quotations WHERE company_id = company_id) as total_quotations,
        (SELECT COUNT(*) FROM leads WHERE company_id = company_id) as total_leads,
        (SELECT COALESCE(SUM(total), 0) FROM invoices WHERE company_id = company_id AND status = 'paid') as total_revenue,
        (SELECT COALESCE(SUM(balance_due), 0) FROM invoices WHERE company_id = company_id AND status IN ('sent', 'partial', 'overdue')) as outstanding_amount,
        (SELECT COUNT(*) FROM invoices WHERE company_id = company_id AND status = 'overdue') as overdue_invoices,
        (SELECT COUNT(*) FROM invoices WHERE company_id = company_id AND MONTH(issue_date) = MONTH(CURDATE()) AND YEAR(issue_date) = YEAR(CURDATE())) as monthly_invoices;
END //

DELIMITER ;