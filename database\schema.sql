-- Business Management Application Database Schema
-- MySQL 5.7+ Compatible

SET foreign_key_checks = 0;
DROP TABLE IF EXISTS `invoice_items`;
DROP TABLE IF EXISTS `quotation_items`;
DROP TABLE IF EXISTS `invoices`;
DROP TABLE IF EXISTS `quotations`;
DROP TABLE IF EXISTS `items`;
DROP TABLE IF EXISTS `customers`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `companies`;
DROP TABLE IF EXISTS `leads`;
DROP TABLE IF EXISTS `contracts`;
DROP TABLE IF EXISTS `payment_records`;
DROP TABLE IF EXISTS `settings`;
DROP TABLE IF EXISTS `activity_log`;
DROP TABLE IF EXISTS `email_templates`;
DROP TABLE IF EXISTS `attachments`;
SET foreign_key_checks = 1;

-- Companies table (for multi-tenant support)
CREATE TABLE companies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    tax_number VARCHAR(100),
    logo_url VARCHAR(500),
    website VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_company_name (name)
);

-- Users table (for authentication)
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
    INDEX idx_user_email (email),
    INDEX idx_user_company (company_id)
);

-- Customers table
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    company VARCHAR(255),
    tax_number VARCHAR(100),
    payment_terms INT DEFAULT 30,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_customer_name (name),
    INDEX idx_customer_email (email),
    INDEX idx_customer_company (company_id)
);

-- Items/Products table
CREATE TABLE items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    sku VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    cost DECIMAL(10,2) DEFAULT 0,
    category VARCHAR(100),
    unit VARCHAR(50) DEFAULT 'piece',
    tax_rate DECIMAL(5,2) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_item_name (name),
    INDEX idx_item_sku (sku),
    INDEX idx_item_category (category),
    INDEX idx_item_company (company_id)
);

-- Quotations table
CREATE TABLE quotations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    quote_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT,
    issue_date DATE NOT NULL,
    expiry_date DATE,
    subtotal DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft',
    terms TEXT,
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_quote_number (quote_number),
    INDEX idx_quote_customer (customer_id),
    INDEX idx_quote_status (status),
    INDEX idx_quote_date (issue_date),
    INDEX idx_quote_company (company_id)
);

-- Quotation items table
CREATE TABLE quotation_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quotation_id INT NOT NULL,
    item_id INT,
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    total DECIMAL(15,2) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE SET NULL,
    INDEX idx_quotation_item_quote (quotation_id),
    INDEX idx_quotation_item_order (sort_order)
);

-- Invoices table
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    quotation_id INT,
    customer_id INT,
    issue_date DATE NOT NULL,
    due_date DATE,
    subtotal DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    tax_rate DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    total DECIMAL(15,2) DEFAULT 0,
    amount_paid DECIMAL(15,2) DEFAULT 0,
    balance_due DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(20) DEFAULT 'draft',
    payment_terms VARCHAR(255),
    terms TEXT,
    notes TEXT,
    created_by INT,
    sent_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (quotation_id) REFERENCES quotations(id) ON DELETE SET NULL,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_invoice_customer (customer_id),
    INDEX idx_invoice_status (status),
    INDEX idx_invoice_date (issue_date),
    INDEX idx_invoice_due_date (due_date),
    INDEX idx_invoice_company (company_id)
);

-- Invoice items table
CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    item_id INT,
    description TEXT NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    discount_percent DECIMAL(5,2) DEFAULT 0,
    total DECIMAL(15,2) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE SET NULL,
    INDEX idx_invoice_item_invoice (invoice_id),
    INDEX idx_invoice_item_order (sort_order)
);

-- Insert sample company
INSERT INTO companies (name, email, phone, address) VALUES 
('Sample Business Inc.', '<EMAIL>', '******-0123', '123 Business St, Business City, BC 12345');

-- Insert sample admin user
INSERT INTO users (company_id, email, password_hash, first_name, last_name, role) VALUES 
(1, '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin');

-- Insert sample customers
INSERT INTO customers (company_id, name, email, phone, address, company) VALUES 
(1, 'John Smith', '<EMAIL>', '******-0001', '456 Customer Ave, Customer City, CC 67890', 'Smith Enterprises'),
(1, 'Jane Doe', '<EMAIL>', '******-0002', '789 Client Blvd, Client Town, CT 54321', 'Doe Solutions');

-- Leads table
CREATE TABLE leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    company VARCHAR(255),
    source VARCHAR(100) DEFAULT 'website',
    status VARCHAR(255) DEFAULT 'new',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_lead_status (status),
    INDEX idx_lead_company (company_id)
);

-- Contracts table
CREATE TABLE contracts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    contract_number VARCHAR(50) UNIQUE NOT NULL,
    customer_id INT,
    title VARCHAR(255) NOT NULL,
    start_date DATE,
    end_date DATE,
    value DECIMAL(15,2) DEFAULT 0,
    status VARCHAR(255) DEFAULT 'draft',
    terms TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_contract_number (contract_number),
    INDEX idx_contract_customer (customer_id),
    INDEX idx_contract_status (status)
);

-- Insert sample items
INSERT INTO items (company_id, name, description, price, category) VALUES 
(1, 'Web Design Service', 'Custom website design and development', 2500.00, 'Services'),
(1, 'SEO Optimization', 'Search engine optimization package', 800.00, 'Services'),
(1, 'Logo Design', 'Professional logo design', 500.00, 'Design'),
(1, 'Hosting Service', 'Annual web hosting service', 120.00, 'Hosting');

-- Insert sample leads
INSERT INTO leads (company_id, name, email, phone, company, source, status) VALUES 
(1, 'Michael Johnson', '<EMAIL>', '******-0003', 'Tech Startup Inc', 'website', 'new'),
(1, 'Sarah Wilson', '<EMAIL>', '******-0004', 'Retail Co', 'referral', 'contacted');

-- Payment records table
CREATE TABLE payment_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_date DATE NOT NULL,
    payment_method VARCHAR(100) DEFAULT 'cash',
    reference_number VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_payment_invoice (invoice_id),
    INDEX idx_payment_date (payment_date)
);

-- Settings table for application configuration
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(255) DEFAULT 'string',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_company_setting (company_id, setting_key),
    INDEX idx_setting_key (setting_key)
);

-- Activity log table
CREATE TABLE activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    user_id INT,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    old_values TEXT,
    new_values TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_activity_entity (entity_type, entity_id),
    INDEX idx_activity_user (user_id),
    INDEX idx_activity_date (created_at)
);

-- Email templates table
CREATE TABLE email_templates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    name VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    template_type VARCHAR(255) DEFAULT 'custom',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_template_type (template_type),
    INDEX idx_template_company (company_id)
);

-- File attachments table
CREATE TABLE attachments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT DEFAULT 0,
    mime_type VARCHAR(100),
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_attachment_entity (entity_type, entity_id),
    INDEX idx_attachment_user (uploaded_by)
);

-- Insert default settings
INSERT INTO settings (company_id, setting_key, setting_value, setting_type) VALUES
(1, 'invoice_prefix', 'INV', 'string'),
(1, 'quotation_prefix', 'QUO', 'string'),
(1, 'contract_prefix', 'CON', 'string'),
(1, 'default_currency', 'USD', 'string'),
(1, 'default_tax_rate', '10', 'number'),
(1, 'invoice_terms', 'Payment is due within 30 days of invoice date.', 'string'),
(1, 'company_timezone', 'America/New_York', 'string'),
(1, 'date_format', 'Y-m-d', 'string'),
(1, 'enable_notifications', '1', 'boolean'),
(1, 'auto_backup', '1', 'boolean');

-- Insert default email templates
INSERT INTO email_templates (company_id, name, subject, body, template_type) VALUES
(1, 'Invoice Email', 'Invoice #{invoice_number} from {company_name}', 
'Dear {customer_name},\n\nPlease find attached your invoice #{invoice_number} for ${total}.\n\nDue Date: {due_date}\n\nThank you for your business!\n\nBest regards,\n{company_name}', 'invoice'),
(1, 'Quotation Email', 'Quotation #{quote_number} from {company_name}', 
'Dear {customer_name},\n\nPlease find attached your quotation #{quote_number} for ${total}.\n\nThis quotation is valid until {expiry_date}.\n\nWe look forward to working with you!\n\nBest regards,\n{company_name}', 'quotation'),
(1, 'Payment Reminder', 'Payment Reminder - Invoice #{invoice_number}', 
'Dear {customer_name},\n\nThis is a friendly reminder that invoice #{invoice_number} for ${total} was due on {due_date}.\n\nPlease arrange payment at your earliest convenience.\n\nThank you,\n{company_name}', 'reminder');