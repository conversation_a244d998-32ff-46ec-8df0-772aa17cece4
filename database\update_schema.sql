-- Add leads and contracts tables

-- Leads table
CREATE TABLE leads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    source VARCHAR(100),
    status VARCHAR(100) DEFAULT 'new',
    assigned_to INT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_lead_name (name),
    INDEX idx_lead_status (status),
    INDEX idx_lead_assigned_to (assigned_to)
);

-- Contracts table
CREATE TABLE contracts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT,
    customer_id INT,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    start_date DATE,
    end_date DATE,
    total_value DECIMAL(15,2),
    status ENUM('draft', 'active', 'expired', 'terminated') DEFAULT 'draft',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_contract_title (title),
    INDEX idx_contract_customer (customer_id),
    INDEX idx_contract_status (status)
);