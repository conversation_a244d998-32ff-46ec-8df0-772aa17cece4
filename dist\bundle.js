/*! For license information please see bundle.js.LICENSE.txt */
(()=>{var e={287:(e,t)=>{"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),u=Symbol.for("react.context"),l=Symbol.for("react.forward_ref"),i=Symbol.for("react.suspense"),f=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),p=Symbol.iterator,d={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},y=Object.assign,h={};function E(e,t,r){this.props=e,this.context=t,this.refs=h,this.updater=r||d}function v(){}function b(e,t,r){this.props=e,this.context=t,this.refs=h,this.updater=r||d}E.prototype.isReactComponent={},E.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},E.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=E.prototype;var g=b.prototype=new v;g.constructor=b,y(g,E.prototype),g.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,R={current:null},_={key:!0,ref:!0,__self:!0,__source:!0};function x(e,t,n){var a,o={},c=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(c=""+t.key),t)S.call(t,a)&&!_.hasOwnProperty(a)&&(o[a]=t[a]);var u=arguments.length-2;if(1===u)o.children=n;else if(1<u){for(var l=Array(u),i=0;i<u;i++)l[i]=arguments[i+2];o.children=l}if(e&&e.defaultProps)for(a in u=e.defaultProps)void 0===o[a]&&(o[a]=u[a]);return{$$typeof:r,type:e,key:c,ref:s,props:o,_owner:R.current}}function N(e){return"object"==typeof e&&null!==e&&e.$$typeof===r}var C=/\/+/g;function k(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function j(e,t,a,o,c){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var u=!1;if(null===e)u=!0;else switch(s){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case r:case n:u=!0}}if(u)return c=c(u=e),e=""===o?"."+k(u,0):o,w(c)?(a="",null!=e&&(a=e.replace(C,"$&/")+"/"),j(c,t,a,"",function(e){return e})):null!=c&&(N(c)&&(c=function(e,t){return{$$typeof:r,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(c,a+(!c.key||u&&u.key===c.key?"":(""+c.key).replace(C,"$&/")+"/")+e)),t.push(c)),1;if(u=0,o=""===o?".":o+":",w(e))for(var l=0;l<e.length;l++){var i=o+k(s=e[l],l);u+=j(s,t,a,i,c)}else if(i=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"==typeof i)for(e=i.call(e),l=0;!(s=e.next()).done;)u+=j(s=s.value,t,a,i=o+k(s,l++),c);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return u}function $(e,t,r){if(null==e)return e;var n=[],a=0;return j(e,n,"","",function(e){return t.call(r,e,a++)}),n}function P(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var O={current:null},I={transition:null},L={ReactCurrentDispatcher:O,ReactCurrentBatchConfig:I,ReactCurrentOwner:R};function T(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:$,forEach:function(e,t,r){$(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return $(e,function(){t++}),t},toArray:function(e){return $(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=E,t.Fragment=a,t.Profiler=c,t.PureComponent=b,t.StrictMode=o,t.Suspense=i,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=L,t.act=T,t.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=y({},e.props),o=e.key,c=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(c=t.ref,s=R.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(l in t)S.call(t,l)&&!_.hasOwnProperty(l)&&(a[l]=void 0===t[l]&&void 0!==u?u[l]:t[l])}var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){u=Array(l);for(var i=0;i<l;i++)u[i]=arguments[i+2];a.children=u}return{$$typeof:r,type:e.type,key:o,ref:c,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=x,t.createFactory=function(e){var t=x.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:l,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:m,_payload:{_status:-1,_result:e},_init:P}},t.memo=function(e,t){return{$$typeof:f,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=I.transition;I.transition={};try{e()}finally{I.transition=t}},t.unstable_act=T,t.useCallback=function(e,t){return O.current.useCallback(e,t)},t.useContext=function(e){return O.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return O.current.useDeferredValue(e)},t.useEffect=function(e,t){return O.current.useEffect(e,t)},t.useId=function(){return O.current.useId()},t.useImperativeHandle=function(e,t,r){return O.current.useImperativeHandle(e,t,r)},t.useInsertionEffect=function(e,t){return O.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return O.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return O.current.useMemo(e,t)},t.useReducer=function(e,t,r){return O.current.useReducer(e,t,r)},t.useRef=function(e){return O.current.useRef(e)},t.useState=function(e){return O.current.useState(e)},t.useSyncExternalStore=function(e,t,r){return O.current.useSyncExternalStore(e,t,r)},t.useTransition=function(){return O.current.useTransition()},t.version="18.3.1"},424:()=>{},540:(e,t,r)=>{"use strict";e.exports=r(287)},561:()=>{},863:()=>{}},t={};function r(n){var a=t[n];if(void 0!==a)return a.exports;var o=t[n]={exports:{}};return e[n](o,o.exports,r),o.exports}r.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return r.d(t,{a:t}),t},r.d=(e,t)=>{for(var n in t)r.o(t,n)&&!r.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},r.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=r(540);const t=e.createContext({token:null,isAuthenticated:!1,login:()=>{},logout:()=>{},loading:!0}),n=({children:r})=>{const[n,a]=e.useState(null),[o,c]=e.useState(!0);return e.useEffect(()=>{const e=localStorage.getItem("authToken");e&&a(e),c(!1)},[]),e.createElement(t.Provider,{value:{token:n,isAuthenticated:!!n,login:async(e,t)=>{try{const r=await fetch("/api/auth.php",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e,password:t})});if(!r.ok)throw new Error("Login failed");const{token:n}=await r.json();return localStorage.setItem("authToken",n),a(n),{success:!0}}catch(e){return{success:!1,error:e.message}}},logout:()=>{localStorage.removeItem("authToken"),a(null),window.location.reload()},loading:o}},r)},a=()=>e.useContext(t);function o(){const{login:t,loading:r,error:n}=a(),[o,c]=e.useState(""),[s,u]=e.useState(""),[l,i]=e.useState(null);return e.createElement("div",{className:"login-form"},e.createElement("h2",null,"Login"),(l||n)&&e.createElement("div",{className:"error"},l||n),e.createElement("form",{onSubmit:async e=>{e.preventDefault(),i(null);const r=await t(o,s);r.success||i(r.error)}},e.createElement("div",null,e.createElement("label",null,"Email:"),e.createElement("input",{type:"email",value:o,onChange:e=>c(e.target.value),required:!0})),e.createElement("div",null,e.createElement("label",null,"Password:"),e.createElement("input",{type:"password",value:s,onChange:e=>u(e.target.value),required:!0})),e.createElement("button",{type:"submit",disabled:r},r?"Logging in...":"Login")))}var c=r(863),s=r.n(c);function u(){const{logout:t}=a();return e.createElement("button",{onClick:t,className:"text-red-600 hover:text-red-800"},e.createElement("i",{className:"fas fa-sign-out-alt"})," Logout")}function l(){try{const{isAuthenticated:e}=a(),[t,r]=React.useState([]),[n,o]=React.useState(!1),[c,s]=React.useState(""),l=e=>{s(e.target.value)},i=()=>{o(!n)};return React.createElement("header",{className:"bg-white shadow-sm sticky top-0 z-10"},React.createElement("div",{className:"container mx-auto px-6 py-4 flex justify-between items-center"},React.createElement("div",{className:"flex-1 max-w-xl"},React.createElement("div",{className:"relative"},React.createElement("input",{type:"text",placeholder:"Search...",value:c,onChange:l,className:"w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:border-blue-500"}),React.createElement("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"}))),React.createElement("div",{className:"flex items-center space-x-4"},React.createElement("button",{onClick:i,className:"p-2 text-gray-600 hover:text-gray-900 relative"},React.createElement("i",{className:"fas fa-bell"}),t.length>0&&React.createElement("span",{className:"absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"})),e&&React.createElement(u,null))),n&&React.createElement("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200"},React.createElement("div",{className:"p-4"},React.createElement("h3",{className:"text-lg font-semibold mb-2"},"Notifications"),0===t.length?React.createElement("p",{className:"text-gray-500"},"No new notifications"):React.createElement("ul",{className:"space-y-2"},t.map((e,t)=>React.createElement("li",{key:t,className:"p-2 hover:bg-gray-50 rounded"},e.message))))))}catch(e){return console.error("Header component error:",e),reportError(e),React.createElement("div",{className:"bg-red-50 p-4 text-red-600"},"Header Error")}}function i({children:e}){try{return React.createElement(n,null,React.createElement("div",{"data-name":"main-layout",className:"app-container"},React.createElement(s(),null),React.createElement("div",{"data-name":"main-content",className:"content-wrapper"},React.createElement(l,null),React.createElement("main",{"data-name":"page-content",className:"py-6"},e))))}catch(e){return console.error("MainLayout component error:",e),reportError(e),null}}var f=r(561),m=r.n(f),p=r(424),d=r.n(p);function y(){try{const[t,r]=e.useState("dashboard"),[a,c]=e.useState(!1),[s,u]=e.useState(null),l=e.useCallback((e,t="success")=>{u({message:e,type:t}),setTimeout(()=>u(null),5e3)},[]),f=()=>{const r={showNotification:l};switch(t){case"dashboard":default:return e.createElement(d(),r);case"customers":return e.createElement(Customers,r);case"leads":return e.createElement(Leads,r);case"quotations":return e.createElement(Quotations,r);case"invoices":return e.createElement(Invoices,r);case"contracts":return e.createElement(Contracts,r);case"items":return e.createElement(Items,r);case"reports":return e.createElement(Reports,r);case"settings":return e.createElement(Settings,r)}};return e.useEffect(()=>{(async()=>{try{if(window.databaseConnection){const e=await window.databaseConnection.initialize();c(e)}else c(!0)}catch(e){console.error("Database initialization failed:",e),c(!0)}})()},[]),e.useEffect(()=>{const e=()=>{const e=window.location.pathname.substring(1)||"dashboard";r(e)};return window.addEventListener("popstate",e),e(),()=>{window.removeEventListener("popstate",e)}},[]),a?e.createElement(n,null,({isAuthenticated:n})=>e.createElement(e.Fragment,null,n?e.createElement(i,{currentPage:t,setCurrentPage:r},f()):e.createElement("div",{className:"flex justify-center items-center h-screen"},e.createElement(o,null)),s&&e.createElement(m(),{message:s.message,type:s.type,onClose:()=>u(null)}))):e.createElement("div",{className:"flex justify-center items-center h-screen"},e.createElement("div",{className:"text-center"},e.createElement("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),e.createElement("p",null,"Connecting to database..."),e.createElement("p",{className:"text-sm text-gray-500 mt-2"},"Please wait while we establish connection")))}catch(t){return console.error("App component error:",t),reportError(t),e.createElement("div",{className:"p-8 text-red-600"},"An error occurred loading the application. Please try refreshing the page.")}}const h=(0,window.ReactDOM.createRoot)(document.getElementById("root"));class E extends e.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){console.error("Error caught by boundary:",e,t)}render(){return this.state.hasError?e.createElement("div",{className:"p-8 text-red-600"},"Something went wrong. Please refresh the page."):this.props.children}}h.render(e.createElement(E,null,e.createElement(y,null)))})()})();