<?php
header('Content-Type: application/json');

// Check if database config exists
if (!file_exists('config/database.php')) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Database configuration file not found. Please create config/database.php first.'
    ]);
    exit;
}

require_once 'config/database.php';

try {
    // Read and execute schema
    $schema = file_get_contents('database/schema.sql');
    if (!$schema) {
        throw new Exception('Schema file not found');
    }
    
    $statements = explode(';', $schema);
    
    foreach($statements as $statement) {
        $statement = trim($statement);
        if(!empty($statement) && !preg_match('/^(SET|DROP)/i', $statement)) {
            try {
                $pdo->exec($statement);
            } catch(PDOException $e) {
                // Continue if table already exists
                if (strpos($e->getMessage(), 'already exists') === false) {
                    throw $e;
                }
            }
        }
    }
    
    // Execute stored procedures
    $procedures = file_get_contents('database/procedures.sql');
    if ($procedures) {
        $pdo->exec($procedures);
    }
    
    echo json_encode([
        'status' => 'success',
        'message' => 'Database tables and sample data created successfully!'
    ]);
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error setting up database: ' . $e->getMessage()
    ]);
}
?>