<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional Invoice Template Preview</title>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <link href="styles/invoices.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .preview-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        .preview-header {
            text-align: center;
            color: white;
            margin-bottom: 2rem;
        }
        .preview-controls {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1 class="text-4xl font-bold mb-2">Professional Invoice Template</h1>
            <p class="text-xl opacity-90">Industry-Standard Design Preview</p>
        </div>
        
        <div class="preview-controls">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold text-gray-800">Template Preview</h2>
                <div class="flex space-x-3">
                    <button onclick="printInvoice()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                    <button onclick="downloadPDF()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-download mr-2"></i>Download PDF
                    </button>
                </div>
            </div>
        </div>
        
        <div id="invoice-container"></div>
    </div>

    <script src="utils/formatUtils.js"></script>
    <script type="text/babel" src="components/templates/InvoiceTemplate.js"></script>
    
    <script type="text/babel">
        // Sample invoice data for preview
        const sampleInvoiceData = {
            invoiceNumber: 'INV-2024-001',
            status: 'sent',
            createdAt: '2024-01-15',
            dueDate: '2024-02-15',
            customerName: 'Acme Corporation',
            customerCompany: 'Acme Corp Ltd.',
            customerAddress: '123 Business Avenue, Suite 100\nNew York, NY 10001\nUnited States',
            customerEmail: '<EMAIL>',
            customerPhone: '+****************',
            customerGST: 'GST123456789',
            paymentMethod: 'Bank Transfer',
            items: [
                {
                    name: 'Web Development Services',
                    description: 'Custom website development with responsive design',
                    quantity: 1,
                    rate: 5000,
                    total: 5000,
                    details: 'Includes frontend and backend development'
                },
                {
                    name: 'SEO Optimization',
                    description: 'Search engine optimization package',
                    quantity: 1,
                    rate: 1500,
                    total: 1500,
                    details: 'On-page and technical SEO improvements'
                },
                {
                    name: 'Content Management System',
                    description: 'Custom CMS implementation',
                    quantity: 1,
                    rate: 2000,
                    total: 2000,
                    details: 'User-friendly admin panel'
                },
                {
                    name: 'Monthly Maintenance',
                    description: 'Website maintenance and support',
                    quantity: 3,
                    rate: 300,
                    total: 900,
                    details: '3 months prepaid maintenance'
                }
            ],
            subtotal: 9400,
            tax: 940,
            discount: 0,
            total: 10340,
            amountPaid: 5000,
            notes: 'Thank you for choosing our services. Please ensure payment is made within 30 days of the invoice date. For any questions regarding this invoice, please contact our billing department.'
        };

        const sampleCompanySettings = {
            companyName: 'TechSolutions Pro',
            companyAddress: '456 Innovation Drive',
            companyCity: 'San Francisco',
            companyState: 'CA',
            companyZip: '94105',
            companyCountry: 'United States',
            companyPhone: '+****************',
            companyEmail: '<EMAIL>',
            companyWebsite: 'www.techsolutionspro.com',
            companyGST: 'GST*********',
            companyRegistration: 'REG-TSP-2020-001',
            currency: 'USD',
            authorizedName: 'John Smith, CEO',
            bankDetails: 'Bank: First National Bank\nAccount: **********\nRouting: *********\nSWIFT: FNBKUS33',
            terms: 'Payment is due within 30 days of invoice date. Late payments may incur a 1.5% monthly service charge. All work performed is subject to our standard terms and conditions available at www.techsolutionspro.com/terms'
        };

        const sampleTemplateSettings = {
            showLogo: true,
            showSignature: true,
            showBankDetails: true,
            showPaymentTerms: true,
            showTerms: true,
            invoicePrefix: 'INV',
            footerText: 'Thank you for your business! We appreciate your trust in TechSolutions Pro.',
            defaultNotes: 'Please review all items carefully. Contact us immediately if you have any questions or concerns about this invoice.'
        };

        function InvoicePreview() {
            return (
                <div className="bg-white rounded-2xl shadow-2xl overflow-hidden">
                    <InvoiceTemplate 
                        data={sampleInvoiceData}
                        companyInfo={sampleCompanySettings}
                        settings={{
                            company: sampleCompanySettings,
                            templates: sampleTemplateSettings
                        }}
                    />
                </div>
            );
        }

        // Render the preview
        ReactDOM.render(<InvoicePreview />, document.getElementById('invoice-container'));

        // Utility functions
        function printInvoice() {
            window.print();
        }

        function downloadPDF() {
            // This would integrate with your PDF generation utility
            alert('PDF download functionality would be integrated with your existing PDF utils.');
        }

        // Add print styles
        const printStyles = `
            @media print {
                body {
                    background: white !important;
                    padding: 0 !important;
                }
                .preview-header,
                .preview-controls {
                    display: none !important;
                }
                .preview-container {
                    max-width: none !important;
                    padding: 0 !important;
                }
                #invoice-container {
                    box-shadow: none !important;
                    border-radius: 0 !important;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = printStyles;
        document.head.appendChild(styleSheet);
    </script>
</body>
</html>