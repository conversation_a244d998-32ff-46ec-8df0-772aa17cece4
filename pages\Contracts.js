function Contracts() {
    try {
        const [contracts, setContracts] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [showForm, setShowForm] = React.useState(false);
        const [selectedContract, setSelectedContract] = React.useState(null);
        const [editingContract, setEditingContract] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [contractToDelete, setContractToDelete] = React.useState(null);

        React.useEffect(() => {
            fetchContracts();
        }, []);

        const fetchContracts = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.getContracts();
                setContracts(response?.items || []);
            } catch (error) {
                console.error('Error fetching contracts:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load contracts'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleContractSaved = (contract) => {
            if (editingContract) {
                setContracts(prev => prev.map(c => c.objectId === contract.objectId ? contract : c));
            } else {
                setContracts(prev => [contract, ...prev]);
            }
            setShowForm(false);
            setEditingContract(null);
            setNotification({
                type: 'success',
                message: `Contract ${editingContract ? 'updated' : 'created'} successfully`
            });
        };

        const handleEdit = (contract) => {
            console.log('Handle edit called with:', contract); // Debug log
            setEditingContract(contract);
            setShowForm(true);
        };

                const handlePrint = async (contract) => {
            try {
                const settings = await window.apiWrapper.getSettings();
                const companyInfo = settings[0]?.objectData || {};
                const data = contract.objectData;
                const currency = companyInfo?.currency || 'USD';

                const printHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Contract ${data.contractNumber || 'Draft'}</title>
                        <style>
                            body { font-family: ${companyInfo?.templates?.fontFamily || 'Arial, sans-serif'}; font-size: ${companyInfo?.templates?.fontSize || '12px'}; }
                            .container { max-width: 800px; margin: auto; padding: 20px; }
                            .header { color: ${companyInfo?.templates?.headerColor || '#000'}; border-bottom: 2px solid ${companyInfo?.templates?.accentColor || '#333'}; padding-bottom: 20px; margin-bottom: 30px; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>${companyInfo?.companyName || 'Your Company'}</h1>
                                <p>${companyInfo?.companyAddress || ''}</p>
                            </div>
                            <h2>Contract</h2>
                            <p><strong>Contract #:</strong> ${data.contractNumber || 'Draft'}</p>
                            <p><strong>Title:</strong> ${data.title}</p>
                            <p><strong>Customer:</strong> ${data.customerName}</p>
                            <p><strong>Start Date:</strong> ${window.formatDate(data.startDate)}</p>
                            <p><strong>End Date:</strong> ${window.formatDate(data.endDate)}</p>
                            <p><strong>Value:</strong> ${window.formatCurrency(data.value, currency)}</p>
                            <hr/>
                            <div>${data.terms}</div>
                        </div>
                    </body>
                    </html>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printHTML);
                printWindow.document.close();
                printWindow.print();
            } catch (error) {
                console.error('Error printing contract:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to print contract. Make sure settings are configured.',
                });
            }
        };

        const handleDelete = (contract) => {
            setContractToDelete(contract);
            setShowDeleteConfirm(true);
        };

        const confirmDelete = async () => {
            try {
                await window.apiWrapper.deleteObject('contract', contractToDelete.objectId);
                setContracts(prev => prev.filter(c => c.id !== contractToDelete.id));
                setNotification({
                    type: 'success',
                    message: 'Contract deleted successfully'
                });
            } catch (error) {
                console.error('Error deleting contract:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete contract'
                });
            } finally {
                setShowDeleteConfirm(false);
                setContractToDelete(null);
            }
        };

        const handleExport = () => {
            try {
                const csvData = contracts.map(contract => ({
                    'Contract ID': contract.objectData.contractNumber || contract.objectId,
                    'Title': contract.objectData.title || '',
                    'Customer': contract.objectData.customerName || '',
                    'Value': contract.objectData.value || 0,
                    'Status': contract.objectData.status || '',
                    'Start Date': contract.objectData.startDate || '',
                    'End Date': contract.objectData.endDate || '',
                    'Created': formatDate(contract.createdAt)
                }));
                
                const csv = [
                    Object.keys(csvData[0]).join(','),
                    ...csvData.map(row => Object.values(row).join(','))
                ].join('\n');
                
                const blob = new Blob([csv], { type: 'text/csv' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `contracts_${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                URL.revokeObjectURL(url);
                
                setNotification({
                    type: 'success',
                    message: 'Contracts exported successfully'
                });
            } catch (error) {
                console.error('Error exporting contracts:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to export contracts'
                });
            }
        };

        return (
            <div data-name="contracts-page" data-file="pages/Contracts.js">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Contracts</h1>
                    <div className="flex space-x-3">
                        <Button
                            variant="secondary"
                            icon="fas fa-download"
                            onClick={handleExport}
                        >
                            Export CSV
                        </Button>
                        <Button
                            variant="primary"
                            icon="fas fa-plus"
                            onClick={() => setShowForm(true)}
                        >
                            New Contract
                        </Button>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <ContractForm
                        contract={editingContract}
                        onSubmit={handleContractSaved}
                        onCancel={() => {
                            setShowForm(false);
                            setEditingContract(null);
                        }}
                    />
                ) : (
                    <ContractList
                        onContractClick={(contract) => console.log('View contract:', contract)}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                        onPrint={handlePrint}
                    />
                )}

                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete contract "{contractToDelete?.objectData?.title}"? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('Contracts page error:', error);
        reportError(error);
        return null;
    }
}
