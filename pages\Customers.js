import React from 'react';

export default function Customers() {
    try {
        const [showForm, setShowForm] = React.useState(false);
        const [selectedCustomer, setSelectedCustomer] = React.useState(null);
        const [showDetails, setShowDetails] = React.useState(false);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [notification, setNotification] = React.useState(null);
        const [refreshKey, setRefreshKey] = React.useState(0);

        const handleCreateCustomer = () => {
            setSelectedCustomer(null);
            setShowForm(true);
            setShowDetails(false);
        };

        const handleEditCustomer = (customer) => {
            setSelectedCustomer(customer);
            setShowForm(true);
            setShowDetails(false);
        };

        const handleCustomerClick = (customer) => {
            setSelectedCustomer(customer);
            setShowDetails(true);
            setShowForm(false);
        };

        const handleConfirmDeleteCustomer = () => {
            setShowDeleteConfirm(true);
        };

        const handleDeleteCustomer = async () => {
            if (!selectedCustomer) return;
            
            try {
                await window.apiWrapper.deleteObject('customer', selectedCustomer.objectId);
                setNotification({
                    type: 'success',
                    message: 'Customer deleted successfully'
                });
                setShowDeleteConfirm(false);
                setShowDetails(false);
                setSelectedCustomer(null);
                
                // Force refresh by updating key
                setRefreshKey(prev => prev + 1);
            } catch (error) {
                console.error('Error deleting customer:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete customer'
                });
            }
        };

        const handleFormSubmit = () => {
            setShowForm(false);
            setSelectedCustomer(null);
            // Force refresh by updating a key
            setRefreshKey(prev => prev + 1);
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedCustomer(null);
        };

        const handleDetailsClose = () => {
            setShowDetails(false);
            setSelectedCustomer(null);
        };

        return (
            <div data-name="customers-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Customers</h1>
                    <Button
                        onClick={handleCreateCustomer}
                        icon="fas fa-plus"
                    >
                        Add Customer
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedCustomer ? 'Edit Customer' : 'New Customer'}
                        </h2>
                        <CustomerForm
                            customer={selectedCustomer}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showDetails && selectedCustomer ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <CustomerDetails
                            customer={selectedCustomer}
                            onEdit={() => handleEditCustomer(selectedCustomer)}
                            onDelete={handleConfirmDeleteCustomer}
                            onClose={handleDetailsClose}
                        />
                    </div>
                ) : (
                    <CustomerList key={refreshKey} onCustomerClick={handleCustomerClick} />
                )}

                {/* Delete Confirmation Modal */}
                {showDeleteConfirm && (
                    <Modal
                        isOpen={showDeleteConfirm}
                        onClose={() => setShowDeleteConfirm(false)}
                        title="Delete Customer"
                    >
                        <div className="p-4">
                            <p className="mb-4">Are you sure you want to delete this customer? This action cannot be undone.</p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleDeleteCustomer}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Customers page error:', error);
        reportError(error);
        return null;
    }
}
