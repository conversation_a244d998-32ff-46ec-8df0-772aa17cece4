function Dashboard({ showNotification }) {
    try {
        const [stats, setStats] = React.useState({
            totalCustomers: 0,
            totalInvoices: 0,
            totalRevenue: 0,
            pendingInvoices: 0,
            totalLeads: 0,
            totalContracts: 0
        });
        const [recentInvoices, setRecentInvoices] = React.useState([]);
        const [recentLeads, setRecentLeads] = React.useState([]);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            loadDashboardData();
        }, []);

        const loadDashboardData = async () => {
            try {
                setLoading(true);
                
                // Load data using apiWrapper
                const [customersResp, invoicesResp, leadsResp, contractsResp] = await Promise.all([
                    window.apiWrapper.listObjects('customer', 100, true),
                    window.apiWrapper.listObjects('invoice', 100, true),
                    window.apiWrapper.listObjects('lead', 100, true),
                    window.apiWrapper.listObjects('contract', 100, true)
                ]);

                const customers = customersResp.items || [];
                const invoices = invoicesResp.items || [];
                const leads = leadsResp.items || [];
                const contracts = contractsResp.items || [];

                // Calculate stats from objectData
                const totalRevenue = invoices.reduce((sum, inv) => {
                    const amount = parseFloat(inv.objectData?.amount || inv.objectData?.total || 0);
                    return sum + amount;
                }, 0);
                
                const pendingInvoices = invoices.filter(inv => 
                    inv.objectData?.status === 'pending'
                ).length;

                setStats({
                    totalCustomers: customers.length,
                    totalInvoices: invoices.length,
                    totalRevenue,
                    pendingInvoices,
                    totalLeads: leads.length,
                    totalContracts: contracts.length
                });

                setRecentInvoices(invoices.slice(0, 5));
                setRecentLeads(leads.slice(0, 5));

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                showNotification('Failed to load dashboard data', 'error');
            } finally {
                setLoading(false);
            }
        };

        if (loading) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>
            );
        }

        return (
            <div className="space-y-6" data-name="dashboard" data-file="pages/Dashboard.js">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                    <button
                        onClick={loadDashboardData}
                        className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
                    >
                        <i className="fas fa-sync-alt mr-2"></i>
                        Refresh
                    </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <i className="fas fa-users text-blue-600 text-xl"></i>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Customers</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalCustomers}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-green-100 rounded-lg">
                                <i className="fas fa-file-invoice text-green-600 text-xl"></i>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Invoices</p>
                                <p className="text-2xl font-bold text-gray-900">{stats.totalInvoices}</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <div className="p-2 bg-yellow-100 rounded-lg">
                                <i className="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                            </div>
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Revenue</p>
                                <p className="text-2xl font-bold text-gray-900">${formatCurrency(stats.totalRevenue)}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 className="text-lg font-medium text-gray-900">Recent Invoices</h2>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => window.location.hash = '#invoices'}
                                    className="text-sm text-blue-600 hover:text-blue-800"
                                >
                                    View All
                                </button>
                                <button
                                    onClick={() => window.location.hash = '#invoices'}
                                    className="text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                                >
                                    <i className="fas fa-plus mr-1"></i>New
                                </button>
                            </div>
                        </div>
                        <div className="p-6">
                            {recentInvoices.length > 0 ? (
                                <div className="space-y-3">
                                    {recentInvoices.map((invoice) => (
                                        <div key={invoice.objectId} className="flex justify-between items-center p-3 bg-gray-50 rounded hover:bg-gray-100 cursor-pointer"
                                             onClick={() => window.location.hash = '#invoices'}>
                                            <div>
                                                <p className="font-medium">{invoice.objectData?.invoice_number || 'N/A'}</p>
                                                <p className="text-sm text-gray-600">{invoice.objectData?.customer_name || 'Unknown'}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="font-medium">${formatCurrency(invoice.objectData?.amount || invoice.objectData?.total || 0)}</p>
                                                <span className={`text-xs px-2 py-1 rounded-full ${
                                                    invoice.objectData?.status === 'paid' ? 'bg-green-100 text-green-800' :
                                                    invoice.objectData?.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }`}>
                                                    {invoice.objectData?.status || 'unknown'}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <p className="text-gray-500 mb-4">No recent invoices</p>
                                    <button
                                        onClick={() => window.location.hash = '#invoices'}
                                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                                    >
                                        Create First Invoice
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow">
                        <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                            <h2 className="text-lg font-medium text-gray-900">Recent Leads</h2>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => window.location.hash = '#leads'}
                                    className="text-sm text-blue-600 hover:text-blue-800"
                                >
                                    View All
                                </button>
                                <button
                                    onClick={() => window.location.hash = '#leads'}
                                    className="text-sm bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                                >
                                    <i className="fas fa-plus mr-1"></i>New
                                </button>
                            </div>
                        </div>
                        <div className="p-6">
                            {recentLeads.length > 0 ? (
                                <div className="space-y-3">
                                    {recentLeads.map((lead) => (
                                        <div key={lead.objectId} className="flex justify-between items-center p-3 bg-gray-50 rounded hover:bg-gray-100 cursor-pointer"
                                             onClick={() => window.location.hash = '#leads'}>
                                            <div>
                                                <p className="font-medium">{lead.objectData?.name || 'Unknown'}</p>
                                                <p className="text-sm text-gray-600">{lead.objectData?.company || 'N/A'}</p>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm text-gray-600">{lead.objectData?.source || 'Unknown'}</p>
                                                <span className={`text-xs px-2 py-1 rounded-full ${
                                                    lead.objectData?.status === 'new' ? 'bg-blue-100 text-blue-800' :
                                                    lead.objectData?.status === 'contacted' ? 'bg-yellow-100 text-yellow-800' :
                                                    lead.objectData?.status === 'qualified' ? 'bg-purple-100 text-purple-800' :
                                                    lead.objectData?.status === 'converted' ? 'bg-green-100 text-green-800' :
                                                    'bg-red-100 text-red-800'
                                                }`}>
                                                    {lead.objectData?.status || 'unknown'}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <p className="text-gray-500 mb-4">No recent leads</p>
                                    <button
                                        onClick={() => window.location.hash = '#leads'}
                                        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                                    >
                                        Add First Lead
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {/* Quick Actions Section */}
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
                    </div>
                    <div className="p-6">
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <button
                                onClick={() => window.location.hash = '#customers'}
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                            >
                                <i className="fas fa-users text-2xl text-blue-500 mb-2"></i>
                                <span className="text-sm font-medium">Add Customer</span>
                            </button>
                            <button
                                onClick={() => window.location.hash = '#invoices'}
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                            >
                                <i className="fas fa-file-invoice text-2xl text-green-500 mb-2"></i>
                                <span className="text-sm font-medium">Create Invoice</span>
                            </button>
                            <button
                                onClick={() => window.location.hash = '#quotations'}
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                            >
                                <i className="fas fa-file-alt text-2xl text-yellow-500 mb-2"></i>
                                <span className="text-sm font-medium">New Quotation</span>
                            </button>
                            <button
                                onClick={() => window.location.hash = '#leads'}
                                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
                            >
                                <i className="fas fa-user-plus text-2xl text-purple-500 mb-2"></i>
                                <span className="text-sm font-medium">Add Lead</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('Dashboard page error:', error);
        reportError(error);
        return null;
    }
}
