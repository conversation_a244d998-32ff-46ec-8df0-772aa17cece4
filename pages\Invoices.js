function Invoices() {
    try {
        const [showForm, setShowForm] = React.useState(false);
        const [showViewer, setShowViewer] = React.useState(false);
        const [selectedInvoice, setSelectedInvoice] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [companyInfo, setCompanyInfo] = React.useState(null);
        const [loading, setLoading] = React.useState(true);

        React.useEffect(() => {
            loadInitialData();
        }, []);

        const loadInitialData = async () => {
            try {
                setLoading(true);
                const settings = await window.apiWrapper.getSettings();
                const company = settings && settings.length > 0 ? settings[0] : null;
                if (company) {
                    setCompanyInfo(company);
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load company information'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleCreateInvoice = () => {
            setSelectedInvoice(null);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleEditInvoice = (invoice) => {
            setSelectedInvoice(invoice);
            setShowForm(true);
            setShowViewer(false);
        };

        const handleViewInvoice = (invoice) => {
            setSelectedInvoice(invoice);
            setShowViewer(true);
            setShowForm(false);
        };

        const handleInvoiceClick = (invoice) => {
            handleViewInvoice(invoice);
        };

        const handleFormSubmit = async () => {
            setShowForm(false);
            setSelectedInvoice(null);
            setNotification({
                type: 'success',
                message: selectedInvoice 
                    ? 'Invoice updated successfully' 
                    : 'Invoice created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            window.location.reload();
        };

        const handleFormCancel = () => {
            setShowForm(false);
            setSelectedInvoice(null);
        };

        const handleViewerClose = () => {
            setShowViewer(false);
            setSelectedInvoice(null);
        };

        const handlePrint = (invoice) => {
            try {
                console.log('Print invoice:', invoice);
                const printWindow = window.open('', '_blank');
                if (!printWindow) {
                    alert('Please allow popups for this site to print invoices.');
                    return;
                }

                const data = invoice.objectData;
                
                const template = companyInfo?.templates?.invoiceTemplate || 'modern';
                const currency = companyInfo?.currency || 'USD';

                const printHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Invoice ${data.invoiceNumber || 'Draft'}</title>
                        <style>
                            body { font-family: ${companyInfo?.templates?.fontFamily || 'Arial, sans-serif'}; font-size: ${companyInfo?.templates?.fontSize || '12px'}; }
                            .container { max-width: 800px; margin: auto; padding: 20px; }
                            .header { color: ${companyInfo?.templates?.headerColor || '#000'}; border-bottom: 2px solid ${companyInfo?.templates?.accentColor || '#333'}; padding-bottom: 20px; margin-bottom: 30px; }
                            /* Add more template-specific styles here */
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>${companyInfo?.companyName || 'Your Company'}</h1>
                                <p>${companyInfo?.companyAddress || ''}</p>
                            </div>
                            <h2>Invoice</h2>
                            <p><strong>Invoice #:</strong> ${data.invoiceNumber || 'Draft'}</p>
                            <p><strong>Date:</strong> ${window.formatDate(invoice.createdAt)}</p>
                            <p><strong>Due Date:</strong> ${window.formatDate(data.dueDate)}</p>
                            <p><strong>Status:</strong> ${data.status.toUpperCase()}</p>
                            
                            <h3>Bill To:</h3>
                            <p><strong>Customer ID:</strong> ${data.customer}</p>

                            <table>
                                <thead>
                                    <tr><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr>
                                </thead>
                                <tbody>
                                    ${data.items?.map(item => `
                                        <tr>
                                            <td>${item.description}</td>
                                            <td>${item.quantity}</td>
                                            <td>${window.formatCurrency(item.price, currency)}</td>
                                            <td>${window.formatCurrency(item.quantity * item.price, currency)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>

                            <div>
                                <p>Subtotal: ${window.formatCurrency(data.subtotal, currency)}</p>
                                <p>Tax (${data.taxRate}%): ${window.formatCurrency(data.tax, currency)}</p>
                                <p><strong>Total: ${window.formatCurrency(data.total, currency)}</strong></p>
                            </div>
                        </div>
                    </body>
                    </html>
                `;
                
                printWindow.document.write(printHTML);
                printWindow.document.close();
                printWindow.focus();
                
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 1000);
                
            } catch (error) {
                console.error('Print error:', error);
                alert('Failed to print invoice: ' + error.message);
            }
        };

        const handleDownloadPDF = async (invoice) => {
            try {
                console.log('Downloading PDF for invoice:', invoice);
                
                if (typeof window.html2pdf === 'undefined') {
                    alert('PDF export library not loaded. Please refresh the page and try again.');
                    return;
                }

                const element = document.querySelector('.template-container');
                if (!element) {
                    alert('Invoice template not found. Please make sure the invoice is displayed.');
                    return;
                }

                const filename = `invoice_${invoice.objectData.invoiceNumber || invoice.objectId}.pdf`;
                console.log('Generating PDF with filename:', filename);
                
                const options = {
                    margin: 10,
                    filename: filename,
                    image: { type: 'jpeg', quality: 0.98 },
                    html2canvas: { 
                        scale: 2, 
                        useCORS: true,
                        allowTaint: true,
                        backgroundColor: '#ffffff',
                        logging: false
                    },
                    jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
                };

                await window.html2pdf().from(element).set(options).save();
                
                setNotification({
                    type: 'success',
                    message: 'Invoice PDF downloaded successfully!'
                });
                
            } catch (error) {
                console.error('PDF export error:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to export PDF: ' + error.message
                });
            }
        };

        if (loading) {
            return (
                <div className="flex justify-center items-center h-64">
                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500"></i>
                </div>
            );
        }

        return (
            <div data-name="invoices-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Invoices</h1>
                    <Button
                        onClick={handleCreateInvoice}
                        icon="fas fa-plus"
                    >
                        Create Invoice
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedInvoice ? 'Edit Invoice' : 'New Invoice'}
                        </h2>
                        <InvoiceForm
                            invoice={selectedInvoice}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : showViewer && selectedInvoice ? (
                    <div>
                        <div className="mb-4">
                            <div className="flex justify-end gap-3 p-4 bg-white rounded-lg shadow-sm">
                                <button
                                    onClick={() => handlePrint(selectedInvoice)}
                                    className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 flex items-center"
                                >
                                    <i className="fas fa-print mr-2"></i>Print
                                </button>
                                <button
                                    onClick={() => handleDownloadPDF(selectedInvoice)}
                                    className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center"
                                >
                                    <i className="fas fa-download mr-2"></i>Download PDF
                                </button>
                                <button
                                    onClick={() => handleEditInvoice(selectedInvoice)}
                                    className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center"
                                >
                                    <i className="fas fa-edit mr-2"></i>Edit
                                </button>
                                <button
                                    onClick={handleViewerClose}
                                    className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 flex items-center"
                                >
                                    <i className="fas fa-times mr-2"></i>Close
                                </button>
                            </div>
                        </div>
                        {companyInfo && (
                            <DocumentViewer
                                type="invoice"
                                data={selectedInvoice}
                                companyInfo={companyInfo}
                                settings={companyInfo}
                                onEdit={() => handleEditInvoice(selectedInvoice)}
                                onClose={handleViewerClose}
                            />
                        )}
                        {!companyInfo && (
                            <div className="bg-white rounded-lg shadow p-6">
                                <div className="text-center">
                                    <i className="fas fa-spinner fa-spin fa-2x text-blue-500 mb-4"></i>
                                    <p>Loading company information...</p>
                                </div>
                            </div>
                        )}
                    </div>
                ) : (
                    <InvoiceList 
                        onInvoiceClick={handleInvoiceClick} 
                        onEdit={handleEditInvoice}
                        onDelete={(invoice) => {
                            if (window.confirm('Are you sure you want to delete this invoice?')) {
                                // Delete logic here
                                console.log('Delete invoice:', invoice);
                            }
                        }}
                    />
                )}
            </div>
        );
    } catch (error) {
        console.error('Invoices page error:', error);
        reportError(error);
        return null;
    }
}