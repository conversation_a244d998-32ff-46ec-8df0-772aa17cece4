import React from 'react';

export default function Leads() {
    try {
        const [view, setView] = React.useState('list'); // list, form, details
        const [selectedLead, setSelectedLead] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);

        const handleCreateLead = () => {
            setSelectedLead(null);
            setView('form');
        };

        const handleEditLead = (lead) => {
            setSelectedLead(lead);
            setView('form');
        };

        const handleLeadClick = (lead) => {
            setSelectedLead(lead);
            setView('details');
        };

        const handleFormSubmit = () => {
            setView('list');
            setSelectedLead(null);
            setNotification({
                type: 'success',
                message: selectedLead ? 'Lead updated successfully' : 'Lead created successfully'
            });
            setTimeout(() => setNotification(null), 3000);
            window.location.reload();
        };

        const handleFormCancel = () => {
            setView('list');
            setSelectedLead(null);
        };

        const handleDetailsClose = () => {
            setView('list');
            setSelectedLead(null);
        };

        const handleDeleteLead = (lead) => {
            setSelectedLead(lead);
            setShowDeleteConfirm(true);
        };

        const handleConfirmDelete = async () => {
            try {
                await trickleDeleteObject('lead', selectedLead.objectId);
                setNotification({
                    type: 'success',
                    message: 'Lead deleted successfully'
                });
                setShowDeleteConfirm(false);
                setSelectedLead(null);
                setView('list');
                window.location.reload();
            } catch (error) {
                console.error('Error deleting lead:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete lead'
                });
            }
        };

        return (
            <div data-name="leads-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Leads</h1>
                    <Button
                        onClick={handleCreateLead}
                        icon="fas fa-plus"
                    >
                        Add Lead
                    </Button>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {view === 'form' ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-6">
                            {selectedLead ? 'Edit Lead' : 'New Lead'}
                        </h2>
                        <LeadForm
                            lead={selectedLead}
                            onSubmit={handleFormSubmit}
                            onCancel={handleFormCancel}
                        />
                    </div>
                ) : view === 'details' ? (
                    <div className="bg-white rounded-lg shadow p-6">
                        <LeadDetails
                            lead={selectedLead}
                            onEdit={() => handleEditLead(selectedLead)}
                            onDelete={() => handleDeleteLead(selectedLead)}
                            onClose={handleDetailsClose}
                        />
                    </div>
                ) : (
                    <LeadList 
                        onLeadClick={handleLeadClick}
                        onDeleteLead={handleDeleteLead}
                    />
                )}

                {showDeleteConfirm && (
                    <Modal
                        isOpen={showDeleteConfirm}
                        onClose={() => setShowDeleteConfirm(false)}
                        title="Delete Lead"
                    >
                        <div className="p-4">
                            <p className="mb-4">Are you sure you want to delete this lead? This action cannot be undone.</p>
                            <div className="flex justify-end space-x-3">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={handleConfirmDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </Modal>
                )}
            </div>
        );
    } catch (error) {
        console.error('Leads page error:', error);
        reportError(error);
        return null;
    }
}
