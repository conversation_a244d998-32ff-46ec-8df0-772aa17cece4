function Quotations() {
    try {
        const [quotations, setQuotations] = React.useState([]);
        const [loading, setLoading] = React.useState(true);
        const [showForm, setShowForm] = React.useState(false);
        const [selectedQuotation, setSelectedQuotation] = React.useState(null);
        const [notification, setNotification] = React.useState(null);
        const [showDeleteConfirm, setShowDeleteConfirm] = React.useState(false);
        const [quotationToDelete, setQuotationToDelete] = React.useState(null);

        React.useEffect(() => {
            fetchQuotations();
        }, []);

        const fetchQuotations = async () => {
            try {
                setLoading(true);
                const response = await window.apiWrapper.getQuotations();
                setQuotations(response || []);
            } catch (error) {
                console.error('Error fetching quotations:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to load quotations'
                });
            } finally {
                setLoading(false);
            }
        };

        const handleQuotationSaved = (quotation) => {
            if (selectedQuotation) {
                setQuotations(prev => prev.map(q => q.objectId === quotation.objectId ? quotation : q));
            } else {
                setQuotations(prev => [quotation, ...prev]);
            }
            setShowForm(false);
            setSelectedQuotation(null);
            setNotification({
                type: 'success',
                message: `Quotation ${selectedQuotation ? 'updated' : 'created'} successfully`
            });
        };

        const handleEdit = (quotation) => {
            setSelectedQuotation(quotation);
            setShowForm(true);
        };

                const handlePrint = async (quotation) => {
            try {
                const settings = await window.apiWrapper.getSettings();
                const companyInfo = settings[0]?.objectData || {};
                const data = quotation.objectData;
                const currency = companyInfo?.currency || 'USD';

                const printHTML = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Quotation ${data.quotationNumber || 'Draft'}</title>
                        <style>
                            body { font-family: ${companyInfo?.templates?.fontFamily || 'Arial, sans-serif'}; font-size: ${companyInfo?.templates?.fontSize || '12px'}; }
                            .container { max-width: 800px; margin: auto; padding: 20px; }
                            .header { color: ${companyInfo?.templates?.headerColor || '#000'}; border-bottom: 2px solid ${companyInfo?.templates?.accentColor || '#333'}; padding-bottom: 20px; margin-bottom: 30px; }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>${companyInfo?.companyName || 'Your Company'}</h1>
                                <p>${companyInfo?.companyAddress || ''}</p>
                            </div>
                            <h2>Quotation</h2>
                            <p><strong>Quotation #:</strong> ${data.quotationNumber || 'Draft'}</p>
                            <p><strong>Date:</strong> ${window.formatDate(quotation.createdAt)}</p>
                            <p><strong>Valid Until:</strong> ${window.formatDate(data.validUntil)}</p>
                            
                            <h3>Bill To:</h3>
                            <p><strong>Customer:</strong> ${data.customerName}</p>

                            <table>
                                <thead>
                                    <tr><th>Description</th><th>Quantity</th><th>Price</th><th>Total</th></tr>
                                </thead>
                                <tbody>
                                    ${data.items?.map(item => `
                                        <tr>
                                            <td>${item.description}</td>
                                            <td>${item.quantity}</td>
                                            <td>${window.formatCurrency(item.price, currency)}</td>
                                            <td>${window.formatCurrency(item.quantity * item.price, currency)}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>

                            <div>
                                <p>Subtotal: ${window.formatCurrency(data.subtotal, currency)}</p>
                                <p>Tax (${data.taxRate}%): ${window.formatCurrency(data.tax, currency)}</p>
                                <p><strong>Total: ${window.formatCurrency(data.total, currency)}</strong></p>
                            </div>
                        </div>
                    </body>
                    </html>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printHTML);
                printWindow.document.close();
                printWindow.print();
            } catch (error) {
                console.error('Error printing quotation:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to print quotation. Make sure settings are configured.',
                });
            }
        };

        const handleDelete = (quotation) => {
            setQuotationToDelete(quotation);
            setShowDeleteConfirm(true);
        };

        const confirmDelete = async () => {
            try {
                await trickleDeleteObject('quotation', quotationToDelete.objectId);
                setQuotations(prev => prev.filter(q => q.objectId !== quotationToDelete.objectId));
                setNotification({
                    type: 'success',
                    message: 'Quotation deleted successfully'
                });
            } catch (error) {
                console.error('Error deleting quotation:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to delete quotation'
                });
            } finally {
                setShowDeleteConfirm(false);
                setQuotationToDelete(null);
            }
        };

        const handleExport = async (format = 'csv') => {
            try {
                if (quotations.length === 0) {
                    setNotification({
                        type: 'info',
                        message: 'There are no quotations to export.'
                    });
                    return;
                }

                if (format === 'csv') {
                    const csvData = quotations.map(quotation => ({
                        'Quotation Number': quotation.objectData.quotationNumber || quotation.objectId,
                        'Customer': quotation.objectData.customerName || '',
                        'Total': quotation.objectData.total || 0,
                        'Status': quotation.objectData.status || '',
                        'Valid Until': quotation.objectData.validUntil || '',
                        'Created': formatDate(quotation.createdAt)
                    }));
                    
                    const csv = [
                        Object.keys(csvData[0]).join(','),
                        ...csvData.map(row => Object.values(row).join(','))
                    ].join('\n');
                    
                    const blob = new Blob([csv], { type: 'text/csv' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `quotations_${new Date().toISOString().split('T')[0]}.csv`;
                    a.click();
                    URL.revokeObjectURL(url);
                } else if (format === 'pdf') {
                    const settings = await window.apiWrapper.getSettings();
                    const companyInfo = settings[0]?.objectData || {};
                    
                    const pdfDoc = await window.PDFLib.PDFDocument.create();
                    const page = pdfDoc.addPage([595, 842]); // A4 size
                    
                    // Add header with company info
                    page.drawText(companyInfo.companyName || 'Your Company', {
                        x: 50,
                        y: 780,
                        size: 16
                    });
                    
                    // Add table with quotations
                    let y = 750;
                    // TODO: Add proper PDF table rendering
                    
                    const pdfBytes = await pdfDoc.save();
                    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `quotations_${new Date().toISOString().split('T')[0]}.pdf`;
                    a.click();
                    URL.revokeObjectURL(url);
                }
                
                setNotification({
                    type: 'success',
                    message: `Quotations exported as ${format.toUpperCase()} successfully`
                });
            } catch (error) {
                console.error('Error exporting quotations:', error);
                setNotification({
                    type: 'error',
                    message: `Failed to export quotations as ${format.toUpperCase()}`
                });
            }
        };

        const handleSendEmail = async (quotation) => {
            try {
                const settings = await window.apiWrapper.getSettings();
                const companyInfo = settings[0]?.objectData || {};
                const customer = await window.apiWrapper.getCustomer(quotation.objectData.customer);
                
                // TODO: Implement actual email sending API call
                setNotification({
                    type: 'info',
                    message: `Email would be sent to ${customer.email}`
                });
            } catch (error) {
                console.error('Error sending quotation email:', error);
                setNotification({
                    type: 'error',
                    message: 'Failed to send quotation email'
                });
            }
        };

        const handleDuplicate = (quotation) => {
            const newQuotation = {
                ...quotation,
                objectId: `temp_${Date.now()}`,
                objectData: {
                    ...quotation.objectData,
                    quotationNumber: `${quotation.objectData.quotationNumber}_COPY`
                }
            };
            setShowForm(true);
            setSelectedQuotation(newQuotation);
            setNotification({
                type: 'success',
                message: 'Quotation duplicated for editing'
            });
        };

        const handlePreview = (quotation) => {
            // TODO: Implement modal preview
            window.open(`/quotation-preview/${quotation.objectId}`, '_blank');
            setNotification({
                type: 'info',
                message: 'Opening quotation preview'
            });
        };

        return (
            <div data-name="quotations-page" data-file="pages/Quotations.js">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Quotations</h1>
                    <div className="flex space-x-3">
                        <div className="dropdown">
                            <Button
                                variant="secondary"
                                icon="fas fa-download"
                                onClick={() => handleExport('csv')}
                            >
                                Export
                            </Button>
                            <div className="dropdown-content">
                                <button onClick={() => handleExport('csv')}>Export as CSV</button>
                                <button onClick={() => handleExport('pdf')}>Export as PDF</button>
                            </div>
                        </div>
                        <Button
                            variant="primary"
                            icon="fas fa-plus"
                            onClick={() => setShowForm(true)}
                        >
                            New Quotation
                        </Button>
                    </div>
                </div>

                {notification && (
                    <Notification
                        type={notification.type}
                        message={notification.message}
                        onClose={() => setNotification(null)}
                    />
                )}

                {showForm ? (
                    <QuotationForm
                        quotation={selectedQuotation}
                        onSubmit={handleQuotationSaved}
                        onCancel={() => {
                            setShowForm(false);
                            setSelectedQuotation(null);
                        }}
                    />
                ) : (
                <QuotationList
                    onQuotationClick={(quotation) => console.log('View quotation:', quotation)}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onPrint={handlePrint}
                    onSendEmail={handleSendEmail}
                    onDuplicate={handleDuplicate}
                    onPreview={handlePreview}
                />
                )}

                {showDeleteConfirm && (
                    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                            <h3 className="text-lg font-medium mb-4">Confirm Delete</h3>
                            <p className="text-gray-600 mb-6">
                                Are you sure you want to delete quotation "{quotationToDelete?.objectData?.quotationNumber}"? This action cannot be undone.
                            </p>
                            <div className="flex justify-end space-x-4">
                                <Button
                                    variant="secondary"
                                    onClick={() => setShowDeleteConfirm(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="danger"
                                    onClick={confirmDelete}
                                >
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        );
    } catch (error) {
        console.error('Quotations page error:', error);
        reportError(error);
        return null;
    }
}
