function Reports({ showNotification }) {
    try {
        const [reportData, setReportData] = React.useState(null);
        const [loading, setLoading] = React.useState(true);
        const [selectedDateRange, setSelectedDateRange] = React.useState('last30days');
        const [customDateRange, setCustomDateRange] = React.useState({
            startDate: '',
            endDate: ''
        });

        React.useEffect(() => {
            loadReportData();
        }, [selectedDateRange, customDateRange]);

        const loadReportData = async () => {
            try {
                setLoading(true);

                let startDate, endDate;
                const today = new Date();
                switch (selectedDateRange) {
                    case 'last7days':
                        startDate = new Date(today.setDate(today.getDate() - 7)).toISOString().split('T')[0];
                        break;
                    case 'last30days':
                        startDate = new Date(today.setDate(today.getDate() - 30)).toISOString().split('T')[0];
                        break;
                    case 'last90days':
                        startDate = new Date(today.setDate(today.getDate() - 90)).toISOString().split('T')[0];
                        break;
                    case 'thisyear':
                        startDate = new Date(new Date().getFullYear(), 0, 1).toISOString().split('T')[0];
                        break;
                    case 'custom':
                        startDate = customDateRange.startDate;
                        endDate = customDateRange.endDate;
                        break;
                    default:
                        break;
                }

                const data = await window.apiWrapper.getReports(startDate, endDate);
                setReportData(data);
            } catch (error) {
                console.error('Error loading reports:', error);
                showNotification('Failed to load reports', 'error');
            } finally {
                setLoading(false);
            }
        };

        const handleExportCSV = () => {
            try {
                if (!reportData) return;
                
                const csvData = [
                    ['Metric', 'Value'],
                    ['Total Invoices', reportData.salesSummary.total_invoices],
                    ['Total Revenue', reportData.salesSummary.total_revenue],
                    ['Paid Revenue', reportData.salesSummary.paid_revenue],
                    ['Overdue Amount', reportData.salesSummary.overdue_amount],
                    ['Total Customers', reportData.salesSummary.total_customers],
                    ['Total Leads', reportData.salesSummary.total_leads]
                ];

                const csvContent = csvData.map(row => row.join(',')).join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `business_report_${new Date().toISOString().split('T')[0]}.csv`;
                a.click();
                window.URL.revokeObjectURL(url);
                
                showNotification('Report exported successfully', 'success');
            } catch (error) {
                console.error('Export error:', error);
                showNotification('Failed to export report', 'error');
            }
        };

        const handlePrintReport = () => {
            window.print();
        };

        if (loading) {
            return (
                <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                </div>
            );
        }

        return (
            <div data-name="reports-page">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Reports & Analytics</h1>
                    <div className="flex space-x-3">
                        <select
                            value={selectedDateRange}
                            onChange={(e) => setSelectedDateRange(e.target.value)}
                            className="border border-gray-300 rounded px-3 py-2"
                        >
                            <option value="last7days">Last 7 Days</option>
                            <option value="last30days">Last 30 Days</option>
                            <option value="last90days">Last 90 Days</option>
                            <option value="thisyear">This Year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                        <button
                            onClick={handleExportCSV}
                            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center"
                        >
                            <i className="fas fa-download mr-2"></i>Export CSV
                        </button>
                        <button
                            onClick={handlePrintReport}
                            className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600 flex items-center"
                        >
                            <i className="fas fa-print mr-2"></i>Print
                        </button>
                        <button
                            onClick={loadReportData}
                            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 flex items-center"
                        >
                            <i className="fas fa-sync-alt mr-2"></i>Refresh
                        </button>
                    </div>
                </div>

                {selectedDateRange === 'custom' && (
                    <div className="bg-white rounded-lg shadow p-4 mb-6">
                        <div className="flex space-x-4 items-center">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                                <input
                                    type="date"
                                    value={customDateRange.startDate}
                                    onChange={(e) => setCustomDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                                    className="border border-gray-300 rounded px-3 py-2"
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                                <input
                                    type="date"
                                    value={customDateRange.endDate}
                                    onChange={(e) => setCustomDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                                    className="border border-gray-300 rounded px-3 py-2"
                                />
                            </div>
                            <button
                                onClick={loadReportData}
                                className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 mt-6"
                            >
                                Apply Filter
                            </button>
                        </div>
                    </div>
                )}

                <ReportsDashboard reportData={reportData} showNotification={showNotification} />
            </div>
        );
    } catch (error) {
        console.error('Reports page error:', error);
        reportError(error);
        return null;
    }
}