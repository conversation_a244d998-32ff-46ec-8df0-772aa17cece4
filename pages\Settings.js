import React from 'react';

export default function Settings() {
    const [formData, setFormData] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [saving, setSaving] = React.useState(false);
    const [notification, setNotification] = React.useState(null);
    const [logoPreview, setLogoPreview] = React.useState('');
    const [signaturePreview, setSignaturePreview] = React.useState('');
    const [activeTab, setActiveTab] = React.useState('company');
    const [settingsId, setSettingsId] = React.useState(null);
    const [showPreview, setShowPreview] = React.useState(false);
    const [previewType, setPreviewType] = React.useState('');

    React.useEffect(() => {
        loadSettings();
    }, []);

    const loadSettings = async () => {
        try {
            setLoading(true);
            const loadedData = await window.apiWrapper.getSettings();
            setFormData(loadedData || {});
            if (loadedData.logo) setLogoPreview(loadedData.logo);
            if (loadedData.signature) setSignaturePreview(loadedData.signature);
            if(loadedData.id) setSettingsId(loadedData.id);
        } catch (error) {
            console.error('Error loading settings:', error);
            setNotification({ type: 'error', message: 'Failed to load settings' });
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, [name]: type === 'checkbox' ? checked : value }));
    };

    const handleTemplateChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({ ...prev, templates: { ...prev.templates, [name]: type === 'checkbox' ? checked : value } }));
    };

    const handleMarginChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, templates: { ...prev.templates, margins: { ...(prev.templates?.margins), [name]: parseInt(value) || 0 } } }));
    };

    const handleNotificationChange = (key) => {
        setFormData(prev => ({ ...prev, notifications: { ...(prev.notifications), [key]: !prev.notifications?.[key] } }));
    };

    const handleFileUpload = (e, type) => {
        const file = e.target.files[0];
        if (!file) return;
        const reader = new FileReader();
        reader.onloadend = () => {
            if (type === 'logo') {
                setLogoPreview(reader.result);
                setFormData(prev => ({ ...prev, logo: reader.result }));
            } else if (type === 'signature') {
                setSignaturePreview(reader.result);
                setFormData(prev => ({ ...prev, signature: reader.result }));
            }
        };
        reader.readAsDataURL(file);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            setSaving(true);
            if (settingsId) {
                await window.apiWrapper.updateSettings({ ...formData, id: settingsId });
            } else {
                const response = await window.apiWrapper.createSettings(formData);
                setSettingsId(response.id);
            }
            setNotification({ type: 'success', message: 'Settings saved successfully' });
        } catch (error) {
            console.error('Error saving settings:', error);
            setNotification({ type: 'error', message: 'Failed to save settings' });
        } finally {
            setSaving(false);
        }
    };

    const handleOpenPreview = (type) => {
        setPreviewType(type);
        setShowPreview(true);
    };

    const handleClosePreview = () => {
        setShowPreview(false);
        setPreviewType('');
    };

    const renderActiveTab = () => {
        switch (activeTab) {
            case 'company': return <CompanySettings formData={formData} handleInputChange={handleInputChange} />;
            case 'branding': return <BrandingSettings formData={formData} logoPreview={logoPreview} signaturePreview={signaturePreview} handleFileUpload={handleFileUpload} setLogoPreview={setLogoPreview} setSignaturePreview={setSignaturePreview} setFormData={setFormData} />;
            case 'payment': return <PaymentSettings formData={formData} handleInputChange={handleInputChange} />;
            case 'templates': return <TemplateSettings formData={formData} handleTemplateChange={handleTemplateChange} handleMarginChange={handleMarginChange} handleOpenPreview={handleOpenPreview} />;
            case 'notifications': return <NotificationSettings formData={formData} handleNotificationChange={handleNotificationChange} />;
            case 'preferences': return <PreferenceSettings formData={formData} handleInputChange={handleInputChange} />;
            default: return <CompanySettings formData={formData} handleInputChange={handleInputChange} />;
        }
    };

    if (loading) {
        return <div className="p-6">Loading...</div>;
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {notification && <Notification type={notification.type} message={notification.message} onClose={() => setNotification(null)} />}
            
            <div className="max-w-7xl mx-auto">
                <div className="flex justify-between items-center mb-6 pb-4 border-b">
                    <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
                    <Button onClick={handleSubmit} disabled={saving} className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300">
                        <i className={`fas fa-save mr-2 ${saving ? 'animate-spin' : ''}`}></i>
                        {saving ? 'Saving...' : 'Save All Changes'}
                    </Button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
                    <div className="lg:col-span-3">
                        <div className="bg-white rounded-lg shadow-md p-4 sticky top-6">
                            <nav className="space-y-2">
                                <SettingsNavItem icon="fa-building" label="Company" tabName="company" activeTab={activeTab} setActiveTab={setActiveTab} />
                                <SettingsNavItem icon="fa-palette" label="Branding" tabName="branding" activeTab={activeTab} setActiveTab={setActiveTab} />
                                <SettingsNavItem icon="fa-credit-card" label="Payment" tabName="payment" activeTab={activeTab} setActiveTab={setActiveTab} />
                                <SettingsNavItem icon="fa-file-alt" label="Templates" tabName="templates" activeTab={activeTab} setActiveTab={setActiveTab} />
                                <SettingsNavItem icon="fa-bell" label="Notifications" tabName="notifications" activeTab={activeTab} setActiveTab={setActiveTab} />
                                <SettingsNavItem icon="fa-sliders-h" label="Preferences" tabName="preferences" activeTab={activeTab} setActiveTab={setActiveTab} />
                            </nav>
                        </div>
                    </div>

                    <div className="lg:col-span-9">
                        <div className="bg-white rounded-lg shadow-md p-6 min-h-[600px]">
                            {renderActiveTab()}
                        </div>
                    </div>
                </div>
            </div>

            <Modal
                isOpen={showPreview}
                onClose={handleClosePreview}
                title={`${previewType.charAt(0).toUpperCase() + previewType.slice(1)} Preview`}
                size="xl"
                closeOnOverlayClick={true}
            >
                <div className="w-full p-4">
                    <TemplatePreview 
                        type={previewType} 
                        companyInfo={{
                            ...formData,
                            settings: formData.templates,
                            logo: logoPreview,
                            signature: signaturePreview
                        }} 
                        onClose={handleClosePreview} 
                    />
                </div>
            </Modal>
        </div>
    );
}

function SettingsNavItem({ icon, label, tabName, activeTab, setActiveTab }) {
    const isActive = activeTab === tabName;
    return (
        <a href="#" 
           onClick={(e) => { e.preventDefault(); setActiveTab(tabName); }} 
           className={`flex items-center px-4 py-2.5 rounded-lg text-sm font-medium transition duration-200 ${isActive ? 'bg-blue-100 text-blue-700 shadow-sm' : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'}`}>
            <i className={`fas ${icon} w-5 mr-3 ${isActive ? 'text-blue-600' : 'text-gray-400'}`}></i>
            <span>{label}</span>
        </a>
    );
}
