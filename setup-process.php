<?php
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);

try {
    // Create config directory if it doesn't exist
    if (!is_dir('config')) {
        mkdir('config', 0755, true);
    }
    
    // Generate database config file
    $configContent = "<?php\n";
    $configContent .= "// Database configuration\n";
    $configContent .= "\$host = '" . addslashes($data['host']) . "';\n";
    $configContent .= "\$dbname = '" . addslashes($data['dbname']) . "';\n";
    $configContent .= "\$username = '" . addslashes($data['username']) . "';\n";
    $configContent .= "\$password = '" . addslashes($data['password']) . "';\n\n";
    $configContent .= "try {\n";
    $configContent .= "    \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);\n";
    $configContent .= "    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);\n";
    $configContent .= "    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);\n";
    $configContent .= "} catch(PDOException \$e) {\n";
    $configContent .= "    http_response_code(500);\n";
    $configContent .= "    header('Content-Type: application/json');\n";
    $configContent .= "    echo json_encode(['error' => 'Database connection failed: ' . \$e->getMessage()]);\n";
    $configContent .= "    exit;\n";
    $configContent .= "}\n";
    $configContent .= "?>";
    
    // Write config file
    file_put_contents('config/database.php', $configContent);
    
    // Test connection
    $pdo = new PDO("mysql:host={$data['host']};dbname={$data['dbname']};charset=utf8mb4", $data['username'], $data['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Run installation
    $installResponse = file_get_contents('http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/install.php');
    $installResult = json_decode($installResponse, true);
    
    if ($installResult && $installResult['status'] === 'success') {
        echo json_encode(['success' => true, 'message' => 'Database setup completed successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Database setup failed']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>