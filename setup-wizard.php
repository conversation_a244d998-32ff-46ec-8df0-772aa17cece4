<?php
// Database Installation Wizard for BhaviCRM
session_start();

// Check if already installed
if (file_exists('config/installed.lock')) {
    header('Location: index.php');
    exit;
}

$step = $_GET['step'] ?? 1;
$error = '';
$success = '';

// Handle form submissions
if ($_POST) {
    switch ($step) {
        case 1:
            // Database connection test
            $host = $_POST['host'] ?? 'localhost';
            $username = $_POST['username'] ?? 'root';
            $password = $_POST['password'] ?? '';
            $dbname = $_POST['dbname'] ?? 'business_management';
            
            try {
                // Test connection without database
                $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Create database if it doesn't exist
                $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                
                // Store connection details in session
                $_SESSION['db_config'] = [
                    'host' => $host,
                    'username' => $username,
                    'password' => $password,
                    'dbname' => $dbname
                ];
                
                $success = "Database connection successful! Database '$dbname' is ready.";
                $step = 2;
            } catch (PDOException $e) {
                $error = "Database connection failed: " . $e->getMessage();
            }
            break;
            
        case 2:
            // Install database schema
            if (!isset($_SESSION['db_config'])) {
                header('Location: setup-wizard.php?step=1');
                exit;
            }
            
            $config = $_SESSION['db_config'];
            
            try {
                $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4", 
                              $config['username'], $config['password']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Read and execute schema
                $schema = file_get_contents('database/schema.sql');
                $pdo->exec($schema);
                
                // Read and execute procedures
                if (file_exists('database/procedures.sql')) {
                    $procedures = file_get_contents('database/procedures.sql');
                    $pdo->exec($procedures);
                }
                
                $success = "Database schema installed successfully!";
                $step = 3;
            } catch (PDOException $e) {
                $error = "Schema installation failed: " . $e->getMessage();
            }
            break;
            
        case 3:
            // Create admin user and company
            if (!isset($_SESSION['db_config'])) {
                header('Location: setup-wizard.php?step=1');
                exit;
            }
            
            $config = $_SESSION['db_config'];
            $companyName = $_POST['company_name'] ?? 'My Business';
            $companyEmail = $_POST['company_email'] ?? '';
            $adminEmail = $_POST['admin_email'] ?? '';
            $adminPassword = $_POST['admin_password'] ?? '';
            $adminName = $_POST['admin_name'] ?? 'Admin User';
            
            if (empty($adminEmail) || empty($adminPassword)) {
                $error = "Admin email and password are required.";
                break;
            }
            
            try {
                $pdo = new PDO("mysql:host={$config['host']};dbname={$config['dbname']};charset=utf8mb4", 
                              $config['username'], $config['password']);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Update company information
                $stmt = $pdo->prepare("UPDATE companies SET name = ?, email = ? WHERE id = 1");
                $stmt->execute([$companyName, $companyEmail]);
                
                // Update admin user
                $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
                $nameParts = explode(' ', $adminName, 2);
                $firstName = $nameParts[0];
                $lastName = $nameParts[1] ?? '';
                
                $stmt = $pdo->prepare("UPDATE users SET email = ?, password_hash = ?, first_name = ?, last_name = ? WHERE id = 1");
                $stmt->execute([$adminEmail, $passwordHash, $firstName, $lastName]);
                
                // Create config file
                $configContent = "<?php
// Database configuration
\$host = '{$config['host']}';
\$dbname = '{$config['dbname']}';
\$username = '{$config['username']}';
\$password = '{$config['password']}';

try {
    \$pdo = new PDO(\"mysql:host=\$host;dbname=\$dbname;charset=utf8mb4\", \$username, \$password);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException \$e) {
    http_response_code(500);
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Database connection failed: ' . \$e->getMessage()]);
    exit;
}
?>";
                
                file_put_contents('config/database.php', $configContent);
                
                // Create installation lock file
                file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
                
                $success = "Installation completed successfully!";
                $step = 4;
            } catch (PDOException $e) {
                $error = "Setup failed: " . $e->getMessage();
            }
            break;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BhaviCRM Installation Wizard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-2">BhaviCRM Installation</h1>
                <p class="text-gray-600">Professional Business Management System</p>
            </div>

            <!-- Progress Steps -->
            <div class="mb-8">
                <div class="flex items-center justify-center space-x-4">
                    <?php for ($i = 1; $i <= 4; $i++): ?>
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold
                                <?= $step >= $i ? 'bg-blue-600' : 'bg-gray-400' ?>">
                                <?= $i ?>
                            </div>
                            <?php if ($i < 4): ?>
                                <div class="w-16 h-1 <?= $step > $i ? 'bg-blue-600' : 'bg-gray-300' ?>"></div>
                            <?php endif; ?>
                        </div>
                    <?php endfor; ?>
                </div>
                <div class="flex justify-between mt-2 text-sm text-gray-600">
                    <span>Database</span>
                    <span>Schema</span>
                    <span>Setup</span>
                    <span>Complete</span>
                </div>
            </div>

            <!-- Main Content -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <?php if ($error): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                            <span class="text-red-800"><?= htmlspecialchars($error) ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($success): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <span class="text-green-800"><?= htmlspecialchars($success) ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                    <!-- Step 1: Database Connection -->
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Database Configuration</h2>
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Database Host</label>
                            <input type="text" name="host" value="<?= $_POST['host'] ?? 'localhost' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Database Name</label>
                            <input type="text" name="dbname" value="<?= $_POST['dbname'] ?? 'business_management' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                            <input type="text" name="username" value="<?= $_POST['username'] ?? 'root' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                            <input type="password" name="password" value="<?= $_POST['password'] ?? '' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-database mr-2"></i>Test Connection & Create Database
                        </button>
                    </form>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Install Schema -->
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Install Database Schema</h2>
                    <p class="text-gray-600 mb-6">This will create all necessary tables and initial data for your CRM system.</p>
                    
                    <form method="POST">
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-cogs mr-2"></i>Install Database Schema
                        </button>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Step 3: Company & Admin Setup -->
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Company & Admin Setup</h2>
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                            <input type="text" name="company_name" value="<?= $_POST['company_name'] ?? 'My Business' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Company Email</label>
                            <input type="email" name="company_email" value="<?= $_POST['company_email'] ?? '' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Admin Name</label>
                            <input type="text" name="admin_name" value="<?= $_POST['admin_name'] ?? 'Admin User' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                            <input type="email" name="admin_email" value="<?= $_POST['admin_email'] ?? '' ?>" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Admin Password</label>
                            <input type="password" name="admin_password" 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" required>
                        </div>
                        
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-user-shield mr-2"></i>Complete Setup
                        </button>
                    </form>

                <?php elseif ($step == 4): ?>
                    <!-- Step 4: Installation Complete -->
                    <div class="text-center">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-check text-3xl text-green-600"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-800 mb-4">Installation Complete!</h2>
                        <p class="text-gray-600 mb-8">Your BhaviCRM system has been successfully installed and configured.</p>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                            <h3 class="font-semibold text-blue-800 mb-2">Login Details:</h3>
                            <p class="text-blue-700">Email: <?= htmlspecialchars($_POST['admin_email'] ?? '<EMAIL>') ?></p>
                            <p class="text-blue-700">Password: [Your chosen password]</p>
                        </div>
                        
                        <a href="index.php" class="inline-block bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-arrow-right mr-2"></i>Go to Dashboard
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Footer -->
            <div class="text-center mt-8 text-gray-500">
                <p>&copy; 2024 BhaviCRM. Professional Business Management System.</p>
            </div>
        </div>
    </div>
</body>
</html>