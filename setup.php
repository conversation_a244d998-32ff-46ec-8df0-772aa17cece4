<?php
// Setup wizard for database configuration
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Business Management - Setup</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-md">
            <h1 class="text-2xl font-bold mb-6 text-center">Database Setup</h1>
            
            <form id="setupForm">
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Database Host</label>
                    <input type="text" id="host" value="localhost" class="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Database Name</label>
                    <input type="text" id="dbname" value="business_management" class="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500">
                </div>
                
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                    <input type="text" id="username" value="root" class="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500">
                </div>
                
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                    <input type="password" id="password" class="w-full px-3 py-2 border rounded focus:outline-none focus:border-blue-500">
                </div>
                
                <button type="submit" class="w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
                    Setup Database
                </button>
            </form>
            
            <div id="result" class="mt-4 text-center"></div>
        </div>
    </div>

    <script>
        document.getElementById('setupForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                host: document.getElementById('host').value,
                dbname: document.getElementById('dbname').value,
                username: document.getElementById('username').value,
                password: document.getElementById('password').value
            };
            
            try {
                const response = await fetch('setup-process.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                const resultDiv = document.getElementById('result');
                
                if (result.success) {
                    resultDiv.innerHTML = '<p class="text-green-600">Setup completed successfully!</p>';
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 2000);
                } else {
                    resultDiv.innerHTML = '<p class="text-red-600">Error: ' + result.message + '</p>';
                }
            } catch (error) {
                document.getElementById('result').innerHTML = '<p class="text-red-600">Setup failed: ' + error.message + '</p>';
            }
        });
    </script>
</body>
</html>