.invoice-form {
    max-width: 800px;
    margin: 0 auto;
}

.invoice-header {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.invoice-items {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.invoice-item {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr auto;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.invoice-item:last-child {
    border-bottom: none;
}

.invoice-totals {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 2px solid #e5e7eb;
}

.invoice-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.invoice-status.draft {
    background-color: #f3f4f6;
    color: #4b5563;
}

.invoice-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.invoice-status.paid {
    background-color: #d1fae5;
    color: #065f46;
}

.invoice-status.overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

.payment-history {
    margin-top: 2rem;
    padding: 1rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
}

.payment-record {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.payment-record:last-child {
    border-bottom: none;
}

/* Professional Invoice Template Styles */
.professional-invoice {
    background-color: white;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border: 1px solid #f3f4f6;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.professional-invoice-header {
    position: relative;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    overflow: hidden;
}

.professional-invoice-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -25%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.professional-invoice-title {
    font-size: 3rem;
    font-weight: 900;
    color: #1f2937;
    letter-spacing: -0.025em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.professional-invoice-status {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.professional-invoice-meta {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.professional-invoice-section {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease;
}

.professional-invoice-section:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
}

.professional-invoice-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.professional-invoice-table thead {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.professional-invoice-table th {
    padding: 1rem 1.5rem;
    text-align: left;
    color: #374151;
    font-weight: 700;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border-bottom: 2px solid #e5e7eb;
    position: relative;
}

.professional-invoice-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
    opacity: 0.1;
}

.professional-invoice-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    color: #374151;
    font-weight: 500;
}

.professional-invoice-table tbody tr {
    transition: background-color 0.15s ease;
}

.professional-invoice-table tbody tr:hover {
    background-color: #f9fafb;
}

.professional-invoice-table tbody tr:last-child td {
    border-bottom: none;
}

.professional-invoice-totals {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.professional-invoice-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e5e7eb;
    font-weight: 500;
}

.professional-invoice-total-row:last-child {
    border-bottom: 2px solid #3b82f6;
    padding-top: 1rem;
    font-weight: 700;
    font-size: 1.25rem;
    color: #1f2937;
}

.professional-invoice-notes {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: 0.75rem;
    padding: 1rem;
    margin: 1.5rem 0;
}

.professional-invoice-signature-section {
    border-top: 2px solid #e5e7eb;
    padding-top: 2rem;
    margin-top: 2rem;
}

.professional-invoice-signature-box {
    text-align: center;
    padding: 1rem;
}

.professional-invoice-signature-line {
    width: 12rem;
    height: 4rem;
    border-bottom: 2px solid #6b7280;
    margin: 0 auto 1rem;
    position: relative;
}

.professional-invoice-signature-line::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 2px;
    background: #3b82f6;
}

.professional-invoice-footer {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 2rem;
}

.professional-invoice-footer-text {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.professional-invoice-footer-meta {
    font-size: 0.875rem;
    color: #d1d5db;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Status-specific styles */
.invoice-status-paid {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border: 1px solid #22c55e;
}

.invoice-status-overdue {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #dc2626;
    border: 1px solid #ef4444;
}

.invoice-status-sent {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1d4ed8;
    border: 1px solid #3b82f6;
}

.invoice-status-draft {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    border: 1px solid #6b7280;
}

/* Enhanced typography */
.professional-invoice h1,
.professional-invoice h2,
.professional-invoice h3,
.professional-invoice h4 {
    font-weight: 700;
    color: #1f2937;
}

.professional-invoice .text-primary {
    color: #3b82f6;
}

.professional-invoice .text-success {
    color: #059669;
}

.professional-invoice .text-warning {
    color: #d97706;
}

.professional-invoice .text-danger {
    color: #dc2626;
}

/* Print optimizations */
@media print {
    .professional-invoice {
        box-shadow: none;
        border: none;
        max-width: none;
        margin: 0;
    }
    
    .professional-invoice-header::before {
        display: none;
    }
    
    .professional-invoice-section:hover {
        box-shadow: none;
    }
    
    .professional-invoice-table tbody tr:hover {
        background-color: transparent;
    }
}

/* Payment status badges */
.payment-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.payment-status-badge.paid {
    background-color: #d1fae5;
    color: #065f46;
}

.payment-status-badge.overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

.payment-status-badge.pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* UPI QR code */
.upi-qr-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    margin-top: 1.5rem;
}

.upi-qr-code {
    width: 120px;
    height: 120px;
    margin-bottom: 0.5rem;
}

.upi-id {
    font-size: 0.75rem;
    color: #6b7280;
}

/* Due date indicator */
.due-date-indicator {
    display: flex;
    align-items: center;
    margin-top: 0.5rem;
}

.due-date-indicator.overdue {
    color: #ef4444;
}

.due-date-indicator.upcoming {
    color: #f59e0b;
}

.due-date-indicator.paid {
    color: #10b981;
}

@media (max-width: 768px) {
    .invoice-header {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .invoice-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .enhanced-invoice-header {
        flex-direction: column;
    }
    
    .enhanced-invoice-meta {
        padding: 1rem;
    }
    
    .enhanced-invoice-table th, 
    .enhanced-invoice-table td {
        padding: 0.75rem 0.5rem;
    }
}
