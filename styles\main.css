body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

#root {
  min-height: 100vh;
}

.flex {
  display: flex;
}

.justify-center {
  justify-content: center;
}

.items-center {
  align-items: center;
}

.h-screen {
  height: 100vh;
}

.text-center {
  text-align: center;
}

.shadow-sm {
  box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1);
}

.sticky {
  position: sticky;
  top: 0;
}

.z-10 {
  z-index: 10;
}

.bg-white {
  background-color: white;
}

/* Basic form styles */
.login-form {
  max-width: 400px;
  width: 100%;
}

.error {
  color: red;
  margin-bottom: 1rem;
}
