/* Common template styles */
.template-container {
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    max-width: 800px;
    margin: 0 auto;
    color: #1a202c;
    font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    position: relative;
    box-sizing: border-box;
}

/* A4 size specifications for better printing */
.a4-page {
    width: 210mm;
    min-height: 297mm;
    padding: 15mm;
    margin: 0 auto;
    page-break-after: always;
    box-sizing: border-box;
}

/* Letter size specifications */
.letter-page {
    width: 215.9mm;
    min-height: 279.4mm;
    padding: 15mm;
    margin: 0 auto;
    page-break-after: always;
    box-sizing: border-box;
}

/* Legal size specifications */
.legal-page {
    width: 215.9mm;
    min-height: 355.6mm;
    padding: 15mm;
    margin: 0 auto;
    page-break-after: always;
    box-sizing: border-box;
}

/* A5 size specifications */
.a5-page {
    width: 148.5mm;
    min-height: 210mm;
    padding: 10mm;
    margin: 0 auto;
    page-break-after: always;
    box-sizing: border-box;
}

/* Orientation styles */
.portrait {
    /* Default orientation */
}

.landscape {
    width: 297mm;
    min-height: 210mm;
}

.letter-page.landscape {
    width: 279.4mm;
    min-height: 215.9mm;
}

.legal-page.landscape {
    width: 355.6mm;
    min-height: 215.9mm;
}

.a5-page.landscape {
    width: 210mm;
    min-height: 148.5mm;
}

.template-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.5rem;
    background: linear-gradient(90deg, #3b82f6, #6366f1);
    border-radius: 0.5rem 0.5rem 0 0;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.template-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 0.25rem;
    letter-spacing: 0.025em;
}

.template-number {
    color: #6b7280;
    font-size: 0.875rem;
}

.company-logo {
    max-height: 3.5rem;
    margin-bottom: 0.5rem;
}

.company-details {
    text-align: right;
    font-size: 0.875rem;
}

.company-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1e3a8a;
    margin-bottom: 0.25rem;
}

.info-section {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

.info-section h3 {
    text-transform: uppercase;
    font-size: 0.7rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.5rem;
    letter-spacing: 0.05em;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.info-item h4 {
    font-size: 0.7rem;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-item p {
    color: #374151;
    font-size: 0.875rem;
    margin: 0.25rem 0;
}

/* Enhanced items table with better spacing */
.items-container {
    margin: 1.5rem 0;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    overflow: hidden;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 0;
    font-size: 0.875rem;
}

.items-table th {
    background-color: #f3f4f6;
    padding: 0.75rem;
    text-align: left;
    font-weight: 600;
    color: #4b5563;
    font-size: 0.7rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
}

.items-table td {
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
    color: #374151;
    font-size: 0.875rem;
    vertical-align: top;
}

.items-table tr:last-child td {
    border-bottom: none;
}

.items-table .empty-row td {
    height: 2rem;
    border-bottom: 1px solid #e5e7eb;
}

.items-table .empty-row:last-child td {
    border-bottom: none;
}

.totals-section {
    display: flex;
    justify-content: flex-end;
    margin: 1.5rem 0;
}

.totals-table {
    width: 16rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    overflow: hidden;
    background-color: #f9fafb;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-bottom: 1px solid #e5e7eb;
}

.totals-row:last-child {
    border-bottom: none;
}

.totals-label {
    color: #4b5563;
}

.totals-value {
    font-weight: 500;
    color: #1f2937;
}

.grand-total {
    background-color: #eef2ff;
    padding: 0.75rem 1rem;
    font-size: 1rem;
}

.grand-total-label {
    font-weight: 600;
    color: #1f2937;
}

.grand-total-value {
    font-weight: 700;
    color: #1e3a8a;
    font-size: 1.125rem;
}

.notes-section {
    margin-bottom: 1.5rem;
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    padding: 1rem;
}

.notes-section h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.notes-content {
    color: #6b7280;
    font-size: 0.8rem;
    white-space: pre-line;
}

.payment-instructions h3 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.signature-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
}

.signature-box {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.signature-line {
    width: 100%;
    border-bottom: 1px solid #9ca3af;
    margin-bottom: 0.5rem;
    position: relative;
    min-height: 3rem;
}

.signature-image {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    max-height: 3rem;
    max-width: 80%;
}

.signature-name {
    font-weight: 600;
    font-size: 0.8rem;
    color: #1f2937;
}

.signature-title {
    font-size: 0.7rem;
    color: #6b7280;
}

.template-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
    color: #6b7280;
    font-size: 0.7rem;
}

.template-footer p {
    margin-bottom: 0.25rem;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.7rem;
    font-weight: 500;
}

.status-draft {
    background-color: #f3f4f6;
    color: #4b5563;
}

.status-sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.status-accepted, .status-paid {
    background-color: #d1fae5;
    color: #065f46;
}

.status-rejected, .status-overdue {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-signed {
    background-color: #d1fae5;
    color: #065f46;
}

.watermark {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-45deg);
    font-size: 5rem;
    opacity: 0.05;
    font-weight: 900;
    color: #1e3a8a;
    white-space: nowrap;
    pointer-events: none;
    text-transform: uppercase;
}

/* Contract specific styles */
.contract-header {
    text-align: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.75rem;
    border-bottom: 2px solid #e5e7eb;
}

.contract-section {
    margin-bottom: 1.5rem;
}

.contract-section h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #1f2937;
}

.contract-parties {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.party-details {
    padding: 0.75rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    font-size: 0.85rem;
}

.contract-terms {
    margin-bottom: 1.5rem;
}

.contract-signature {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* Print styles */
@media print {
    body {
        margin: 0;
        padding: 0;
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    .template-container {
        box-shadow: none;
        padding: 15mm;
        max-width: 100%;
        width: 210mm;
        height: 297mm;
        margin: 0;
        border-radius: 0;
    }
    
    .template-container::before {
        display: none;
    }

    .a4-page {
        padding: 15mm;
        margin: 0;
        border: none;
        width: 100%;
        height: 100%;
    }
    
    @page {
        size: A4;
        margin: 0;
    }
    
    .watermark {
        opacity: 0.03;
    }
    
    /* Scale down elements if needed to fit A4 */
    .items-table {
        font-size: 0.8rem;
    }
    
    .info-section,
    .notes-section,
    .signature-section {
        margin-bottom: 1rem;
    }
    
    /* Ensure table formatting is preserved */
    .items-table th {
        background-color: #f3f4f6 !important;
        color: #4b5563;
        font-weight: 600;
    }
    
    .items-table tr:nth-child(even) {
        background-color: #f9fafb !important;
    }
    
    /* Status badges */
    .status-badge, .invoice-status, .quotation-status, .contract-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .status-active, .status-paid, .status-accepted, .status-signed {
        background-color: #d1fae5 !important;
        color: #065f46 !important;
    }
    
    .status-inactive, .status-overdue, .status-rejected {
        background-color: #fee2e2 !important;
        color: #991b1b !important;
    }
    
    .status-draft {
        background-color: #f3f4f6 !important;
        color: #4b5563 !important;
    }
    
    .status-sent, .status-pending {
        background-color: #dbeafe !important;
        color: #1e40af !important;
    }
    
    /* Avoid page breaks in the middle of important sections */
    .items-table,
    .totals-section,
    .signature-section,
    .notes-section {
        page-break-inside: avoid;
    }
    
    /* Ensure images print properly */
    img {
        max-width: 100%;
    }
    
    .company-logo {
        max-height: 50px;
    }
    
    .signature-image {
        max-height: 40px;
    }
    
    /* Ensure proper colors in print */
    .grand-total {
        background-color: #eef2ff !important;
    }
}

/* Responsive styles for mobile and smaller screens */
@media (max-width: 640px) {
    .template-header {
        flex-direction: column;
    }
    
    .company-details {
        text-align: left;
        margin-top: 1rem;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .signature-section {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .contract-parties {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .contract-signature {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .items-table {
        font-size: 0.75rem;
    }
    
    .items-table th, .items-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .template-container {
        padding: 1rem;
    }
}

/* Document viewer styles */
.document-viewer-container {
    background-color: #f3f4f6;
    min-height: 100vh;
    padding: 2rem;
}

.document-actions-bar {
    background-color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

/* Add some breathing room around the document */
.document-preview-wrapper {
    padding: 2rem 0;
}

/* Template preview in settings */
.template-preview-container {
    background-color: #f3f4f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 2rem;
    overflow: hidden;
}

.template-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.template-preview-title {
    font-weight: 600;
    color: #4b5563;
}

.template-preview-actions {
    display: flex;
    gap: 0.5rem;
}

.template-preview-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transform-origin: top center;
    max-height: 500px;
    overflow-y: auto;
}

.template-preview-content.scaled {
    transform: scale(0.7);
}

/* Template selector styles */
.template-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.template-option {
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.template-option:hover {
    border-color: #93c5fd;
    background-color: #eff6ff;
}

.template-option.selected {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.template-option-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.template-option-preview {
    height: 100px;
    background-color: #f3f4f6;
    border-radius: 0.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Color picker styles */
.color-picker-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
}

.color-swatch {
    width: 2rem;
    height: 2rem;
    border-radius: 0.25rem;
    border: 1px solid #e5e7eb;
    cursor: pointer;
}

.color-picker-input {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.color-picker-input input[type="color"] {
    border: none;
    padding: 0;
    width: 2rem;
    height: 2rem;
    cursor: pointer;
}

.color-picker-input input[type="text"] {
    width: 7rem;
    padding: 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.25rem;
}
