// API Wrapper for Local Storage Database
window.apiWrapper = {
    // Local storage operations
    getFromStorage(key) {
        try {
            const data = JSON.parse(localStorage.getItem(key) || '[]');
            return { items: data };
        } catch (error) {
            console.error('Error reading from storage:', error);
            return { items: [] };
        }
    },
    
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('Error saving to storage:', error);
            return false;
        }
    },

    // Generic list objects method
    async listObjects(objectType, limit = 100, descent = true, nextPageToken) {
        return this.getFromStorage(`app_${objectType}s`);
    },

    // Generic create object method
    async createObject(objectType, objectData) {
        return this.createObjectLocal(objectType, objectData);
    },

    // Local storage create method
    createObjectLocal(objectType, objectData) {
        const storageKey = `app_${objectType}s`;
        const existing = this.getFromStorage(storageKey);
        const newObject = {
            objectId: Date.now().toString(),
            objectType: objectType,
            objectData: objectData,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        
        existing.items.push(newObject);
        this.saveToStorage(storageKey, existing.items);
        return newObject;
    },

    // Generic update object method
    async updateObject(objectType, objectId, objectData) {
        return this.updateObjectLocal(objectType, objectId, objectData);
    },

    // Local storage update method
    updateObjectLocal(objectType, objectId, objectData) {
        const storageKey = `app_${objectType}s`;
        const existing = this.getFromStorage(storageKey);
        const index = existing.items.findIndex(item => item.objectId === objectId);
        
        if (index !== -1) {
            existing.items[index].objectData = { ...existing.items[index].objectData, ...objectData };
            existing.items[index].updatedAt = new Date().toISOString();
            this.saveToStorage(storageKey, existing.items);
            return existing.items[index];
        }
        
        throw new Error(`Object ${objectId} not found`);
    },

    // Generic delete object method
    async deleteObject(objectType, objectId) {
        return this.deleteObjectLocal(objectType, objectId);
    },

    // Local storage delete method
    deleteObjectLocal(objectType, objectId) {
        const storageKey = `app_${objectType}s`;
        const existing = this.getFromStorage(storageKey);
        const filtered = existing.items.filter(item => item.objectId !== objectId);
        this.saveToStorage(storageKey, filtered);
        return true;
    },

    async getSettings() {
        try {
            const response = await fetch('api/settings.php');
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            return data.length > 0 ? data[0] : {};
        } catch (error) {
            console.error('Error fetching settings:', error);
            return {};
        }
    },

    async createSettings(settingsData) {
        try {
            const response = await fetch('api/settings.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settingsData)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error creating settings:', error);
            throw error;
        }
    },

    async getContracts() {
        try {
            console.log('Fetching contracts from localStorage...'); // Debug log
            const result = await this.listObjects('contract');
            console.log('Contracts data retrieved:', result); // Debug log
            
            if (!result || !result.items) {
                console.warn('No valid contracts data found, returning empty array');
                return [];
            }
            return result;
        } catch (error) {
            console.error('Error in getContracts:', error);
            // Return empty array to prevent UI crash
            return { items: [] };
        }
    },

    async getQuotations() {
        return this.listObjects('quotation');
    },

    async updateSettings(settingsData) {
        try {
            const response = await fetch('api/settings.php', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settingsData)
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error updating settings:', error);
            throw error;
        }
    },

    async getReports(startDate, endDate) {
        try {
            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            const response = await fetch(`api/reports.php?${params.toString()}`);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('Error fetching reports:', error);
            // Return a default structure on error to prevent UI crashes
            return {
                salesSummary: { total_invoices: 0, total_revenue: 0, paid_revenue: 0, overdue_amount: 0, total_customers: 0, total_leads: 0 },
                monthlyRevenue: [],
                topCustomers: []
            };
        }
    }
};
