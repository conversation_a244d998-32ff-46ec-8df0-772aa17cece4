// --- SETTINGS API ---
window.createSettings = async function(settings) {
    const res = await fetch(`${API_BASE}/settings.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    });
    if (!res.ok) throw new Error('Failed to create settings');
    return await res.json();
};

window.updateSettings = async function(settings) {
    const res = await fetch(`${API_BASE}/settings.php`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
    });
    if (!res.ok) throw new Error('Failed to update settings');
    return await res.json();
};
// --- ITEMS API ---
window.fetchItems = async function() {
    const res = await fetch(`${API_BASE}/items.php`);
    if (!res.ok) throw new Error('Failed to fetch items');
    return await res.json();
};

window.createItem = async function(item) {
    const res = await fetch(`${API_BASE}/items.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(item)
    });
    if (!res.ok) throw new Error('Failed to create item');
    return await res.json();
};
// --- CONTRACTS API ---
window.fetchContracts = async function() {
    const res = await fetch(`${API_BASE}/contracts.php`);
    if (!res.ok) throw new Error('Failed to fetch contracts');
    return await res.json();
};

window.createContract = async function(contract) {
    const res = await fetch(`${API_BASE}/contracts.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(contract)
    });
    if (!res.ok) throw new Error('Failed to create contract');
    return await res.json();
};
// --- SETTINGS API ---
window.fetchSettings = async function() {
    const res = await fetch(`${API_BASE}/settings.php`);
    if (!res.ok) throw new Error('Failed to fetch settings');
    return await res.json();
};
// --- INVOICES API ---
window.fetchInvoices = async function() {
    const res = await fetch(`${API_BASE}/invoices.php`);
    if (!res.ok) throw new Error('Failed to fetch invoices');
    return await res.json();
};

window.createInvoice = async function(invoiceData) {
    // Validate required fields
    if (!invoiceData.customer_id || !invoiceData.invoice_number || !invoiceData.due_date) {
        throw new Error('Missing required fields: customer_id, invoice_number, and due_date are required');
    }

    // Ensure items exist and have required fields
    if (!invoiceData.items || !Array.isArray(invoiceData.items) || invoiceData.items.length === 0) {
        throw new Error('Invoice must contain at least one item');
    }

    // Transform data to match API expectations
    const payload = {
        customer_id: invoiceData.customer_id,
        invoice_number: invoiceData.invoice_number,
        issue_date: invoiceData.issue_date || new Date().toISOString().split('T')[0],
        due_date: invoiceData.due_date,
        tax_rate: invoiceData.taxRate || 10,
        notes: invoiceData.notes || '',
        items: invoiceData.items.map(item => ({
            description: item.description,
            quantity: item.quantity,
            price: item.price,
            total: item.quantity * item.price
        }))
    };

    try {
        const res = await fetchWithAuth('/invoices.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });
        
        if (!res.ok) {
            const errorData = await res.json().catch(() => ({}));
            throw new Error(errorData.message || 'Failed to create invoice');
        }
        
        return await res.json();
    } catch (error) {
        console.error('Invoice creation error:', error);
        throw new Error(`Invoice creation failed: ${error.message}`);
    }
};
// --- QUOTATIONS API ---
window.fetchQuotations = async function() {
    const res = await fetch(`${API_BASE}/quotations.php`);
    if (!res.ok) throw new Error('Failed to fetch quotations');
    return await res.json();
};

window.createQuotation = async function(quotation) {
    const res = await fetch(`${API_BASE}/quotations.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(quotation)
    });
    if (!res.ok) throw new Error('Failed to create quotation');
    return await res.json();
};
// --- LEADS API ---
window.fetchLeads = async function() {
    const res = await fetch(`${API_BASE}/leads.php`);
    if (!res.ok) throw new Error('Failed to fetch leads');
    return await res.json();
};

window.createLead = async function(lead) {
    const res = await fetch(`${API_BASE}/leads.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lead)
    });
    if (!res.ok) throw new Error('Failed to create lead');
    return await res.json();
};

window.updateLead = async function(lead) {
    const res = await fetch(`${API_BASE}/leads.php`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(lead)
    });
    if (!res.ok) throw new Error('Failed to update lead');
    return await res.json();
};

window.deleteLead = async function(id) {
    const res = await fetch(`${API_BASE}/leads.php`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id })
    });
    if (!res.ok) throw new Error('Failed to delete lead');
    return await res.json();
};

// Database Connection Handler for MySQL via backend API
// Store auth token from login
let authToken = localStorage.getItem('authToken') || '';
const API_BASE = '/api';

const fetchWithAuth = async (url, options = {}) => {
    const headers = options.headers || {};
    headers['Authorization'] = `Bearer ${authToken}`;
    
    const res = await fetch(`${API_BASE}${url}`, {
        ...options,
        headers
    });

    if (res.status === 401) {
        // Handle token expiration here
        throw new Error('Session expired - please login again');
    }

    return res;
};

window.setAuthToken = (token) => {
    authToken = token;
    localStorage.setItem('authToken', token);
};

window.clearAuthToken = () => {
    authToken = '';
    localStorage.removeItem('authToken');
};

window.fetchCustomers = async function() {
    const res = await fetchWithAuth('/customers.php');
    if (!res.ok) throw new Error('Failed to fetch customers');
    return await res.json();
};

window.createCustomer = async function(customer) {
    const res = await fetch(`${API_BASE}/customers.php`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customer)
    });
    if (!res.ok) throw new Error('Failed to create customer');
    return await res.json();
};

window.updateCustomer = async function(customer) {
    const res = await fetch(`${API_BASE}/customers.php`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(customer)
    });
    if (!res.ok) throw new Error('Failed to update customer');
    return await res.json();
};

window.deleteCustomer = async function(id) {
    const res = await fetch(`${API_BASE}/customers.php`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ id })
    });
    if (!res.ok) throw new Error('Failed to delete customer');
    return await res.json();
};
