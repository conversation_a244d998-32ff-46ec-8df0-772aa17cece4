// Formatting utilities
window.formatCurrency = function(amount, currency = 'USD') {
    if (amount === null || amount === undefined || isNaN(amount)) return '0.00';
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount).replace('$', '');
};

window.formatDate = function(date, format = 'short') {
    if (!date) return '';
    
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    
    switch (format) {
        case 'long':
            return d.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        case 'short':
            return d.toLocaleDateString('en-US');
        case 'iso':
            return d.toISOString().split('T')[0];
        default:
            return d.toLocaleDateString('en-US');
    }
};

window.formatDateTime = function(dateTime) {
    if (!dateTime) return '';
    
    const d = new Date(dateTime);
    if (isNaN(d.getTime())) return '';
    
    return d.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};

window.formatNumber = function(number, decimals = 2) {
    if (number === null || number === undefined || isNaN(number)) return '0';
    return new Intl.NumberFormat('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    }).format(number);
};

// Global aliases
window.formatCurrency = window.formatCurrency;
window.formatDate = window.formatDate;
window.formatDateTime = window.formatDateTime;