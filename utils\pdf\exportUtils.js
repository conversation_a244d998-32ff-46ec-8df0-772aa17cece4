// Enhanced PDF export utilities
function enhancedExportToPDF(element, filename, options = {}) {
    return new Promise((resolve, reject) => {
        try {
            if (!window.html2pdf) {
                reject(new Error('PDF library not loaded'));
                return;
            }

            const defaultOptions = {
                margin: 10,
                filename: filename || 'document.pdf',
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: { 
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    logging: false
                },
                jsPDF: { 
                    unit: 'mm', 
                    format: 'a4', 
                    orientation: 'portrait'
                }
            };

            const finalOptions = { ...defaultOptions, ...options };
            
            window.html2pdf()
                .from(element)
                .set(finalOptions)
                .save()
                .then(() => {
                    console.log('PDF exported successfully');
                    resolve(true);
                })
                .catch((error) => {
                    console.error('PDF export failed:', error);
                    reject(error);
                });

        } catch (error) {
            console.error('Export error:', error);
            reject(error);
        }
    });
}

// Simple fallback export function
function simplePDFExport(element, filename) {
    try {
        if (window.html2pdf) {
            window.html2pdf(element, {
                filename: filename || 'document.pdf',
                jsPDF: { format: 'a4' }
            });
            return true;
        } else {
            console.error('PDF library not available');
            return false;
        }
    } catch (error) {
        console.error('Simple PDF export error:', error);
        return false;
    }
}