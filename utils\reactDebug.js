export function checkReact() {
  if (typeof React === 'undefined') {
    console.error('React is not available!');
    return false;
  }

  console.log('React version:', React.version);
  
  try {
    React.createElement('div');
    console.log('React.createElement works');
    return true;
  } catch (e) {
    console.error('React.createElement failed:', e);
    return false;
  }
}

export function checkReactDOM() {
  if (typeof ReactDOM === 'undefined') {
    console.error('ReactDOM is not available!');
    return false;
  }

  console.log('ReactDOM available');
  return true;
}
