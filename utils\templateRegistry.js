window.templateRegistry = {
    _templates: {},
    _callbacks: {},
    _readyPromises: {},
    _resolveFunctions: {},

    init() {
        ['InvoiceTemplate', 'QuotationTemplate', 'ContractTemplate'].forEach(name => {
            this._readyPromises[name] = new Promise(resolve => {
                this._resolveFunctions[name] = resolve;
            });
        });
    },

    register(name, component) {
        this._templates[name] = component;
        if (this._resolveFunctions[name]) {
            this._resolveFunctions[name](component);
        }
        if (this._callbacks[name]) {
            this._callbacks[name].forEach(cb => cb(component));
            delete this._callbacks[name];
        }
    },

    get(name, callback) {
        if (this._templates[name]) {
            callback(this._templates[name]);
        } else {
            if (!this._callbacks[name]) {
                this._callbacks[name] = [];
            }
            this._callbacks[name].push(callback);
        }
    },

    async ready() {
        await Promise.all(Object.values(this._readyPromises));
    }
};

window.templateRegistry.init();