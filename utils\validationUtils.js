function isEmailValid(email) {
    try {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    } catch (error) {
        console.error('isEmailValid error:', error);
        return false;
    }
}

function isPhoneValid(phone) {
    try {
        const re = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
        return re.test(phone);
    } catch (error) {
        console.error('isPhoneValid error:', error);
        return false;
    }
}

function isPasswordStrong(password) {
    try {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        return (
            password.length >= minLength &&
            hasUpperCase &&
            hasLowerCase &&
            hasNumbers &&
            hasSpecialChar
        );
    } catch (error) {
        console.error('isPasswordStrong error:', error);
        return false;
    }
}

function validateRequired(value) {
    try {
        return value !== null && value !== undefined && value.toString().trim() !== '';
    } catch (error) {
        console.error('validateRequired error:', error);
        return false;
    }
}

function validateLength(value, min, max) {
    try {
        const length = value.toString().length;
        return length >= min && length <= max;
    } catch (error) {
        console.error('validateLength error:', error);
        return false;
    }
}

function validateNumber(value, min, max) {
    try {
        const num = Number(value);
        return !isNaN(num) && num >= min && num <= max;
    } catch (error) {
        console.error('validateNumber error:', error);
        return false;
    }
}
